#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
InterV AI - Live Transcription with AI Response
Optimized for executable build with no console and no temp files
"""

import sys
import os
import io
import threading
import time
import json
import traceback
import tempfile
import atexit
import shutil
import ctypes
from collections import deque
from datetime import datetime

# Suppress console output when running as exe (disabled for debugging)
# if getattr(sys, 'frozen', False):
#     sys.stdout = io.StringIO()
#     sys.stderr = io.StringIO()

try:
    import numpy as np
    import requests
    # Use PyAudioWPatch for WASAPI loopback support (system audio capture)
    try:
        import pyaudiowpatch as pyaudio
        PYAUDIO_WPATCH_AVAILABLE = True
        print("✅ PyAudioWPatch available - system audio capture enabled")
    except ImportError:
        import pyaudio
        PYAUDIO_WPATCH_AVAILABLE = False
        print("⚠️ PyAudioWPatch not available - falling back to standard PyAudio")

    import wave
    import re
    import webbrowser
    from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                                 QWidget, QTextEdit, QLabel, QPushButton, QFrame, QSizeGrip,
                                 QDialog, QLineEdit, QSizePolicy)
    from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer, QMetaObject, Q_ARG
    from PyQt5.QtGui import QFont, QPalette, QColor

    # Firebase imports
    import firebase_admin
    from firebase_admin import credentials, firestore

    # Audio device management imports
    from typing import Optional, Callable, List, Tuple, Dict

    # Platform-specific imports for audio device management
    if sys.platform == "win32":
        try:
            from pycaw.pycaw import AudioUtilities, AudioEndpointVolume
            from comtypes import CLSCTX_ALL
            import psutil
            WINDOWS_AUDIO_AVAILABLE = True
        except ImportError:
            WINDOWS_AUDIO_AVAILABLE = False
            print("Warning: Windows audio monitoring libraries not available. Using fallback detection.")
    else:
        WINDOWS_AUDIO_AVAILABLE = False

    # Try faster-whisper first (smaller), fallback to openai-whisper
    try:
        from faster_whisper import WhisperModel
        WHISPER_TYPE = "faster"
    except ImportError:
        try:
            import whisper
            WHISPER_TYPE = "openai"
        except ImportError:
            raise ImportError("Neither faster-whisper nor openai-whisper found")



except ImportError as e:
    if not getattr(sys, 'frozen', False):
        print(f"Missing dependency: {e}")
        print("Install with: pip install PyQt5 pyaudio faster-whisper numpy requests firebase-admin")
    sys.exit(1)

# Global variables for temp management
TEMP_DIR = None
AUDIO_BUFFERS = {}  # In-memory audio storage instead of files

# Firebase Manager Class
class FirebaseManager:
    def __init__(self):
        self.db = None
        self.initialize_firebase()

    def initialize_firebase(self):
        """Initialize Firebase with service account credentials"""
        try:
            # Firebase service account configuration
            firebase_config = ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

            # Initialize Firebase Admin SDK
            if not firebase_admin._apps:
                cred = credentials.Certificate(firebase_config)
                firebase_admin.initialize_app(cred)

            # Initialize Firestore
            self.db = firestore.client()
            print("✅ Firebase initialized successfully")

        except Exception as e:
            print(f"❌ Firebase initialization error: {e}")
            self.db = None

    def validate_user(self, email):
        """
        Validate user and return user data with remaining time
        Returns: (is_valid, remaining_time_seconds, user_data)
        """
        try:
            print(f"🔄 Validating user: {email}")

            if not self.db:
                print("❌ Firebase database not initialized")
                return False, 0, None

            # Get user document from Firestore
            print(f"🔄 Fetching user document for: {email}")
            user_ref = self.db.collection('users').document(email)
            user_doc = user_ref.get(timeout=10)  # Add timeout

            if not user_doc.exists:
                print(f"❌ User {email} not found in database")
                return False, 0, None

            user_data = user_doc.to_dict()
            print(f"🔄 User data retrieved: {user_data}")

            # Check if user is active
            status = user_data.get('status', 'inactive')
            if status != 'active':
                print(f"❌ User {email} status is: {status}")
                return False, 0, user_data

            # Calculate remaining time - use correct Firebase field names
            allocated_minutes = user_data.get('allocated_time_minutes', 0)
            used_minutes = user_data.get('used_minutes', 0)  # This is the correct field name

            remaining_minutes = allocated_minutes - used_minutes

            print(f"🔄 Time calculation: allocated={allocated_minutes}, used={used_minutes}, remaining={remaining_minutes}")
            print(f"🔄 Available fields: {list(user_data.keys())}")

            if remaining_minutes <= 0:
                print(f"❌ No time remaining for user {email}")
                return False, 0, user_data

            # Update last login and ensure used_time_minutes field exists
            try:
                update_data = {
                    'last_login': firestore.SERVER_TIMESTAMP
                }

                # If only old 'used_minutes' field exists, copy to new field
                if 'used_time_minutes' not in user_data and 'used_minutes' in user_data:
                    update_data['used_time_minutes'] = user_data.get('used_minutes', 0)
                    print(f"🔄 Migrating used_minutes to used_time_minutes: {user_data.get('used_minutes', 0)}")

                user_ref.update(update_data)
                print(f"✅ Updated last login for {email}")
            except Exception as e:
                print(f"⚠️ Could not update last login: {e}")

            remaining_seconds = remaining_minutes * 60
            print(f"✅ User {email} validated. Remaining time: {remaining_minutes} minutes ({remaining_seconds} seconds)")
            return True, remaining_seconds, user_data

        except Exception as e:
            print(f"❌ Error validating user {email}: {e}")
            import traceback
            traceback.print_exc()
            return False, 0, None

# Authentication Dialog Class
class UserAuthDialog(QDialog):
    user_authenticated = pyqtSignal(str, int, dict)  # email, remaining_time, user_data
    auth_result_ready = pyqtSignal(bool, int, object, str)  # is_valid, remaining_time, user_data, email

    def __init__(self, parent=None):
        super().__init__(parent)
        self.firebase_manager = FirebaseManager()
        self.is_email_valid = False
        self.setup_ui()
        self.apply_stealth_features()

        # Connect internal signal for thread-safe UI updates
        self.auth_result_ready.connect(self._handle_auth_result)

    def setup_ui(self):
        """Setup the authentication dialog UI"""
        self.setWindowTitle("InterV AI - Login")
        self.setFixedSize(500, 350)
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)

        # Main layout
        layout = QVBoxLayout()
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)

        # Title
        title_label = QLabel("InterV AI Login")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Segoe UI", 20, QFont.Bold))
        title_label.setStyleSheet("color: #1976D2; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # Email input
        email_label = QLabel("Email Address:")
        email_label.setFont(QFont("Segoe UI", 11))
        layout.addWidget(email_label)

        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("Enter your email address")
        self.email_input.setFont(QFont("Segoe UI", 12))
        self.email_input.setFixedHeight(40)
        self.email_input.textChanged.connect(self.validate_email_input)
        self.email_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #E0E0E0;
                border-radius: 8px;
                padding: 8px 12px;
                background-color: #FFFFFF;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #1976D2;
                background-color: #F8F9FF;
            }
        """)
        layout.addWidget(self.email_input)

        # Buttons layout
        button_layout = QHBoxLayout()

        # Contact button
        self.contact_button = QPushButton("Contact Support")
        self.contact_button.setFont(QFont("Segoe UI", 11))
        self.contact_button.setFixedHeight(40)
        self.contact_button.clicked.connect(self.open_whatsapp)
        self.contact_button.setStyleSheet("""
            QPushButton {
                background-color: #25D366;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #22C55E;
            }
        """)

        # Login button
        self.login_button = QPushButton("Login")
        self.login_button.setFont(QFont("Segoe UI", 11))
        self.login_button.setFixedHeight(40)
        self.login_button.setEnabled(False)
        self.login_button.clicked.connect(self.authenticate_user)
        self.login_button.setStyleSheet("""
            QPushButton {
                background-color: #1976D2;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                padding: 8px 16px;
            }
            QPushButton:hover:enabled {
                background-color: #1565C0;
            }
            QPushButton:disabled {
                background-color: #CCCCCC;
            }
        """)

        button_layout.addWidget(self.contact_button)
        button_layout.addWidget(self.login_button)
        layout.addLayout(button_layout)

        # Status label
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setFont(QFont("Segoe UI", 10))
        self.status_label.setWordWrap(True)
        self.status_label.setMinimumHeight(40)
        self.status_label.setMaximumHeight(60)
        layout.addWidget(self.status_label)

        self.setLayout(layout)

    def apply_stealth_features(self):
        """Apply stealth features to hide from screen sharing"""
        try:
            # Windows-specific stealth features
            if sys.platform == "win32":
                import ctypes
                from ctypes import wintypes

                hwnd = int(self.winId())

                # Hide from Alt+Tab
                ctypes.windll.user32.SetWindowLongW(
                    hwnd, -20,  # GWL_EXSTYLE
                    ctypes.windll.user32.GetWindowLongW(hwnd, -20) | 0x00000080  # WS_EX_TOOLWINDOW
                )

                # Hide from screen sharing (experimental)
                ctypes.windll.user32.SetWindowDisplayAffinity(hwnd, 0x00000011)  # WDA_EXCLUDEFROMCAPTURE

        except Exception as e:
            pass  # Silently continue if stealth features fail

    def validate_email_input(self):
        """Validate email input"""
        email = self.email_input.text().strip()

        if not email:
            self.is_email_valid = False
            self.login_button.setEnabled(False)
            return

        # Email format validation
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        is_valid_format = re.match(email_pattern, email.lower())

        if is_valid_format:
            self.is_email_valid = True
            self.login_button.setEnabled(True)
        else:
            self.is_email_valid = False
            self.login_button.setEnabled(False)

    def open_whatsapp(self):
        """Open WhatsApp with predefined message"""
        phone_number = "918104184175"
        message = "Hi, I need help with InterV AI login."
        whatsapp_url = f"https://wa.me/{phone_number}?text={message}"
        webbrowser.open(whatsapp_url)

    def authenticate_user(self):
        """Authenticate user with Firebase"""
        try:
            email = self.email_input.text().strip().lower()

            if not email or not self.is_email_valid:
                self.show_error("Enter valid email")
                return

            print(f"🔄 Starting authentication for: {email}")

            self.login_button.setEnabled(False)
            self.email_input.setEnabled(False)
            self.status_label.setText("🔄 Connecting...")
            self.status_label.setStyleSheet("color: #1976D2;")

            # Test Firebase connection first
            if not self.firebase_manager.db:
                print("❌ Firebase not initialized")
                self.show_error("Server connection failed.\nTry again.")
                return

            # Run authentication in thread
            self.auth_thread = threading.Thread(target=self._authenticate_thread, args=(email,), daemon=True)
            self.auth_thread.start()

        except Exception as e:
            print(f"❌ Authentication error: {e}")
            self.show_error("Authentication error.\nTry again.")

    def _authenticate_thread(self, email):
        """Authentication thread with proper error handling"""
        try:
            print(f"🔄 Validating user: {email}")
            self.status_label.setText("🔄 Validating...")

            # Add timeout for Firebase call
            import signal

            def timeout_handler(signum, frame):
                raise TimeoutError("Firebase call timed out")

            # Set timeout (only on non-Windows or handle differently)
            try:
                if hasattr(signal, 'SIGALRM'):
                    signal.signal(signal.SIGALRM, timeout_handler)
                    signal.alarm(10)  # 10 second timeout
            except:
                pass

            is_valid, remaining_time, user_data = self.firebase_manager.validate_user(email)

            # Clear timeout
            try:
                if hasattr(signal, 'SIGALRM'):
                    signal.alarm(0)
            except:
                pass

            print(f"🔄 Authentication result: valid={is_valid}, time={remaining_time}")

            # Emit signal for thread-safe UI updates
            print(f"🔄 Emitting auth_result_ready signal...")
            # Convert to int but ensure it's within valid range
            remaining_time_int = max(0, min(int(remaining_time), 2147483647))  # Max 32-bit int
            print(f"🔄 Converting time: {remaining_time} -> {remaining_time_int}")
            self.auth_result_ready.emit(is_valid, remaining_time_int, user_data, email)

        except TimeoutError:
            print("❌ Authentication timeout")
            self.auth_result_ready.emit(False, 0, None, email)
        except Exception as e:
            print(f"❌ Authentication thread error: {e}")
            import traceback
            traceback.print_exc()
            self.auth_result_ready.emit(False, 0, None, email)

    def _handle_auth_result(self, is_valid, remaining_time, user_data, email):
        """Handle authentication result in main thread"""
        try:
            print(f"🔄 Handling auth result: is_valid={is_valid}, time={remaining_time}, email={email}")

            if is_valid and remaining_time > 0:
                print(f"✅ Authentication successful for {email}")
                self.status_label.setText("✅ Login successful!\nOpening app...")
                self.status_label.setStyleSheet("color: #2E7D32;")

                # Emit signal with user data
                print(f"🔄 Emitting user_authenticated signal...")
                self.user_authenticated.emit(email, int(remaining_time), user_data or {})
                print(f"✅ Signal emitted successfully")

                # Close dialog after short delay using QTimer in main thread
                print(f"🔄 Closing dialog in 1.5 seconds...")
                close_timer = QTimer(self)
                close_timer.setSingleShot(True)
                close_timer.timeout.connect(lambda: self.close_dialog_with_accept())
                close_timer.start(1500)

            else:
                print(f"❌ Authentication failed for {email}")
                if user_data is None:
                    self.show_error("User not found.\nContact support.")
                elif remaining_time <= 0:
                    self.show_error("No time remaining.\nContact support.")
                else:
                    self.show_error("Access denied.\nContact support.")

        except Exception as e:
            print(f"❌ Error handling auth result: {e}")
            import traceback
            traceback.print_exc()
            self.show_error("Authentication error.\nTry again.")

    def show_error(self, message):
        """Show error message and reset UI"""
        try:
            print(f"❌ Showing error: {message}")
            self.status_label.setText(message)
            self.status_label.setStyleSheet("color: #D32F2F; font-weight: bold;")
            self.login_button.setEnabled(True)
            self.email_input.setEnabled(True)

            # Auto-clear error after 5 seconds using QTimer in main thread
            clear_timer = QTimer(self)
            clear_timer.setSingleShot(True)
            clear_timer.timeout.connect(lambda: self.status_label.setText(""))
            clear_timer.start(5000)

        except Exception as e:
            print(f"❌ Error in show_error: {e}")

    def close_dialog_with_accept(self):
        """Close dialog with accept status"""
        try:
            print("🔄 Closing dialog with accept status...")
            self.accept()
            print("✅ Dialog closed with accept")
        except Exception as e:
            print(f"❌ Error closing dialog: {e}")

    def keyPressEvent(self, event):
        """Handle key press events"""
        if event.key() in (Qt.Key_Return, Qt.Key_Enter):
            if self.login_button.isEnabled():
                self.authenticate_user()
        elif event.key() == Qt.Key_Escape:
            self.reject()
        else:
            super().keyPressEvent(event)

def setup_temp_directory():
    """Create a temporary directory that auto-cleans"""
    global TEMP_DIR
    try:
        TEMP_DIR = tempfile.mkdtemp(prefix="interv_ai_")
        atexit.register(cleanup_temp_directory)
        return TEMP_DIR
    except:
        TEMP_DIR = tempfile.gettempdir()
        return TEMP_DIR

def cleanup_temp_directory():
    """Clean up temporary directory on exit"""
    global TEMP_DIR, AUDIO_BUFFERS
    AUDIO_BUFFERS.clear()  # Clear in-memory buffers
    if TEMP_DIR and os.path.exists(TEMP_DIR) and "interv_ai_" in TEMP_DIR:
        try:
            shutil.rmtree(TEMP_DIR, ignore_errors=True)
        except:
            pass

# Setup temp directory
setup_temp_directory()

# Audio Device Manager Class
class AudioDeviceManager:
    """
    Manages automatic audio device detection and monitoring for seamless device switching.
    """

    def __init__(self, device_change_callback: Optional[Callable] = None):
        """
        Initialize the audio device manager.

        Args:
            device_change_callback: Callback function called when default device changes
        """
        self.audio = pyaudio.PyAudio()
        self.device_change_callback = device_change_callback
        self.monitoring_thread = None
        self.is_monitoring = False
        self.current_default_device = None
        self.last_device_check = 0
        self.device_check_interval = 2.0  # Check every 2 seconds

        # Device cache for performance
        self.device_cache = {}
        self.cache_timestamp = 0
        self.cache_duration = 5.0  # Cache devices for 5 seconds

    def get_system_default_input_device(self) -> Optional[int]:
        """
        Get the system's default audio input device index.

        Returns:
            Device index of the default input device, or None if not found
        """
        if sys.platform == "win32" and WINDOWS_AUDIO_AVAILABLE:
            return self._get_windows_default_input_device()
        else:
            return self._get_fallback_default_input_device()

    def _get_windows_default_input_device(self) -> Optional[int]:
        """
        Get default input device on Windows using pycaw.

        Returns:
            Device index or None if not found
        """
        try:
            # Get default audio input device from Windows
            devices = AudioUtilities.GetMicrophone()
            if not devices:
                return self._get_fallback_default_input_device()

            default_device = devices
            default_device_name = default_device.FriendlyName

            # Find corresponding PyAudio device index
            for i in range(self.audio.get_device_count()):
                try:
                    dev_info = self.audio.get_device_info_by_index(i)
                    if (dev_info['maxInputChannels'] > 0 and
                        default_device_name.lower() in dev_info['name'].lower()):
                        print(f"Found Windows default input device: {i} - {dev_info['name']}")
                        return i
                except Exception:
                    continue

            # If exact match not found, try partial matching
            for i in range(self.audio.get_device_count()):
                try:
                    dev_info = self.audio.get_device_info_by_index(i)
                    if (dev_info['maxInputChannels'] > 0 and
                        any(word in dev_info['name'].lower()
                            for word in default_device_name.lower().split())):
                        print(f"Found Windows default input device (partial match): {i} - {dev_info['name']}")
                        return i
                except Exception:
                    continue

        except Exception as e:
            print(f"Error getting Windows default device: {e}")

        return self._get_fallback_default_input_device()

    def _get_fallback_default_input_device(self) -> Optional[int]:
        """
        Fallback method to get default input device.

        Returns:
            Device index or None if not found
        """
        try:
            # Try to get the default input device (usually index 0 or first available)
            default_device_info = self.audio.get_default_input_device_info()
            if default_device_info and default_device_info['maxInputChannels'] > 0:
                # Find the device index that matches the default device
                for i in range(self.audio.get_device_count()):
                    try:
                        dev_info = self.audio.get_device_info_by_index(i)
                        if (dev_info['name'] == default_device_info['name'] and
                            dev_info['maxInputChannels'] > 0):
                            print(f"Found fallback default input device: {i} - {dev_info['name']}")
                            return i
                    except Exception:
                        continue
        except Exception as e:
            print(f"Error getting default input device info: {e}")

        # Last resort: find first available input device
        for i in range(self.audio.get_device_count()):
            try:
                dev_info = self.audio.get_device_info_by_index(i)
                if dev_info['maxInputChannels'] > 0:
                    print(f"Using first available input device: {i} - {dev_info['name']}")
                    return i
            except Exception:
                continue

        print("No input devices found")
        return None

    def get_available_input_devices(self) -> List[Tuple[int, str]]:
        """
        Get list of available input devices with caching.

        Returns:
            List of (device_index, device_name) tuples
        """
        current_time = time.time()

        # Return cached devices if cache is still valid
        if (current_time - self.cache_timestamp < self.cache_duration and
            self.device_cache):
            return self.device_cache.get('devices', [])

        devices = []
        try:
            for i in range(self.audio.get_device_count()):
                try:
                    dev = self.audio.get_device_info_by_index(i)
                    if dev['maxInputChannels'] > 0:
                        devices.append((i, dev['name']))
                except Exception:
                    continue
        except Exception as e:
            print(f"Error enumerating audio devices: {e}")

        # Update cache
        self.device_cache = {'devices': devices}
        self.cache_timestamp = current_time

        return devices

    def is_device_available(self, device_index: int) -> bool:
        """
        Check if a specific device is still available.

        Args:
            device_index: Device index to check

        Returns:
            True if device is available, False otherwise
        """
        try:
            dev_info = self.audio.get_device_info_by_index(device_index)
            return dev_info['maxInputChannels'] > 0
        except Exception:
            return False

    def start_monitoring(self):
        """Start monitoring for device changes."""
        if self.is_monitoring:
            return

        self.is_monitoring = True
        self.current_default_device = self.get_system_default_input_device()

        self.monitoring_thread = threading.Thread(
            target=self._monitor_device_changes,
            daemon=True
        )
        self.monitoring_thread.start()
        print("Audio device monitoring started")

    def stop_monitoring(self):
        """Stop monitoring for device changes."""
        self.is_monitoring = False
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            self.monitoring_thread.join(timeout=1.0)
        print("Audio device monitoring stopped")

    def _monitor_device_changes(self):
        """Monitor for audio device changes in background thread."""
        while self.is_monitoring:
            try:
                current_time = time.time()

                # Check for device changes at specified interval
                if current_time - self.last_device_check >= self.device_check_interval:
                    new_default_device = self.get_system_default_input_device()

                    # Check if default device changed
                    if (new_default_device != self.current_default_device and
                        new_default_device is not None):

                        old_device = self.current_default_device
                        self.current_default_device = new_default_device

                        print(f"Default audio device changed: {old_device} -> {new_default_device}")

                        # Notify callback about device change
                        if self.device_change_callback:
                            try:
                                self.device_change_callback(old_device, new_default_device)
                            except Exception as e:
                                print(f"Error in device change callback: {e}")

                    self.last_device_check = current_time

                time.sleep(0.5)  # Check every 500ms for responsiveness

            except Exception as e:
                print(f"Error in device monitoring: {e}")
                time.sleep(1.0)  # Wait longer on error

    def get_device_info(self, device_index: int) -> Optional[Dict]:
        """
        Get detailed information about a specific device.

        Args:
            device_index: Device index

        Returns:
            Device information dictionary or None if not found
        """
        try:
            return self.audio.get_device_info_by_index(device_index)
        except Exception:
            return None

    def cleanup(self):
        """Clean up resources."""
        self.stop_monitoring()
        if hasattr(self, 'audio') and self.audio:
            self.audio.terminate()

# System Audio Capture Class for WASAPI Loopback
class SystemAudioCapture:
    """
    Captures system audio output using WASAPI loopback instead of microphone input.
    This allows recording what's playing through speakers/headphones without requiring stereo mix.
    """

    def __init__(self, device_change_callback: Optional[Callable] = None):
        """
        Initialize system audio capture.

        Args:
            device_change_callback: Callback function called when default output device changes
        """
        self.audio = pyaudio.PyAudio()
        self.device_change_callback = device_change_callback
        self.current_loopback_device = None
        self.stream = None
        self.is_capturing = False

        # Audio format settings (matching RealTimeTranscriber)
        self.FORMAT = pyaudio.paInt16
        self.CHANNELS = 1  # Will be adjusted based on device capabilities
        self.RATE = 16000  # Will be adjusted based on device capabilities
        self.CHUNK = 1024

        # Device monitoring for dynamic Bluetooth switching
        self.monitoring_thread = None
        self.is_monitoring = False
        self.device_check_interval = 1.0  # Check every second for faster Bluetooth detection
        self.last_device_check = 0
        self.device_switch_in_progress = False

        # Check WASAPI availability
        self.wasapi_available = self._check_wasapi_availability()
        if not self.wasapi_available:
            print("❌ WASAPI not available - system audio capture will not work")
            return

        # Get default loopback device
        self.current_loopback_device = self._get_default_loopback_device()
        if self.current_loopback_device:
            print(f"✅ Default loopback device found: {self.current_loopback_device['name']}")
            # Start device monitoring for dynamic Bluetooth switching
            self.start_device_monitoring()
        else:
            print("❌ No loopback device found")

    def _check_wasapi_availability(self) -> bool:
        """Check if WASAPI is available on the system."""
        if not PYAUDIO_WPATCH_AVAILABLE:
            return False

        try:
            wasapi_info = self.audio.get_host_api_info_by_type(pyaudio.paWASAPI)
            return True
        except (OSError, AttributeError):
            return False

    def _get_default_loopback_device(self) -> Optional[Dict]:
        """
        Get the default system output device in loopback mode.

        Returns:
            Device info dictionary for loopback device or None if not found
        """
        if not self.wasapi_available:
            return None

        try:
            # Get WASAPI info
            wasapi_info = self.audio.get_host_api_info_by_type(pyaudio.paWASAPI)

            # Get default output device
            default_speakers = self.audio.get_device_info_by_index(wasapi_info["defaultOutputDevice"])

            # If it's already a loopback device, use it
            if default_speakers.get("isLoopbackDevice", False):
                return default_speakers

            # Find corresponding loopback device
            if hasattr(self.audio, 'get_loopback_device_info_generator'):
                for loopback in self.audio.get_loopback_device_info_generator():
                    if default_speakers["name"] in loopback["name"]:
                        return loopback

            # Fallback: search manually through all devices
            for i in range(self.audio.get_device_count()):
                try:
                    device = self.audio.get_device_info_by_index(i)
                    if (device.get("isLoopbackDevice", False) and
                        default_speakers["name"] in device["name"]):
                        return device
                except Exception:
                    continue

        except Exception as e:
            print(f"Error getting default loopback device: {e}")

        return None

    def get_available_loopback_devices(self) -> List[Tuple[int, str]]:
        """
        Get list of available loopback devices.

        Returns:
            List of (device_index, device_name) tuples for loopback devices
        """
        devices = []
        if not self.wasapi_available:
            return devices

        try:
            if hasattr(self.audio, 'get_loopback_device_info_generator'):
                for device in self.audio.get_loopback_device_info_generator():
                    devices.append((device['index'], device['name']))
            else:
                # Fallback: search manually
                for i in range(self.audio.get_device_count()):
                    try:
                        device = self.audio.get_device_info_by_index(i)
                        if device.get("isLoopbackDevice", False):
                            devices.append((i, device['name']))
                    except Exception:
                        continue
        except Exception as e:
            print(f"Error enumerating loopback devices: {e}")

        return devices

    def open_stream(self) -> bool:
        """
        Open audio stream for system audio capture.

        Returns:
            True if stream opened successfully, False otherwise
        """
        if not self.wasapi_available or not self.current_loopback_device:
            return False

        try:
            # Adjust audio settings based on device capabilities
            device = self.current_loopback_device

            # Use device's native sample rate if possible
            sample_rate = int(device.get("defaultSampleRate", self.RATE))
            if sample_rate < 16000:
                sample_rate = 16000  # Minimum for good transcription quality

            # Use device's channel count, but prefer mono for transcription
            channels = min(device.get("maxInputChannels", 2), 2)

            print(f"🎤 Opening system audio stream:")
            print(f"   Device: {device['name']}")
            print(f"   Sample Rate: {sample_rate} Hz")
            print(f"   Channels: {channels}")

            self.stream = self.audio.open(
                format=self.FORMAT,
                channels=channels,
                rate=sample_rate,
                frames_per_buffer=self.CHUNK,
                input=True,
                input_device_index=device["index"]
            )

            # Update instance variables with actual settings
            self.RATE = sample_rate
            self.CHANNELS = channels
            self.is_capturing = True

            print("✅ System audio stream opened successfully")
            return True

        except Exception as e:
            print(f"❌ Error opening system audio stream: {e}")
            return False

    def read_audio_chunk(self) -> Optional[bytes]:
        """
        Read audio chunk from system audio stream.

        Returns:
            Audio data bytes or None if error/no stream
        """
        if not self.stream or not self.is_capturing:
            return None

        try:
            audio_data = self.stream.read(self.CHUNK, exception_on_overflow=False)

            # Convert to mono if stereo (for transcription compatibility)
            if self.CHANNELS == 2:
                # Convert stereo to mono by averaging channels
                audio_array = np.frombuffer(audio_data, dtype=np.int16)
                stereo_data = audio_array.reshape(-1, 2)
                mono_data = np.mean(stereo_data, axis=1, dtype=np.int16)
                audio_data = mono_data.tobytes()

            return audio_data

        except Exception as e:
            print(f"Error reading system audio: {e}")
            return None

    def close_stream(self):
        """Close the audio stream."""
        if self.stream:
            try:
                self.stream.stop_stream()
                self.stream.close()
                print("✅ System audio stream closed")
            except Exception as e:
                print(f"Error closing system audio stream: {e}")
            finally:
                self.stream = None
                self.is_capturing = False

    def refresh_default_device(self) -> bool:
        """
        Refresh the default loopback device (useful when system audio device changes).

        Returns:
            True if new device found and updated, False otherwise
        """
        old_device = self.current_loopback_device
        new_device = self._get_default_loopback_device()

        if new_device and (not old_device or new_device['index'] != old_device['index']):
            self.current_loopback_device = new_device
            print(f"🔄 Default loopback device updated: {new_device['name']}")

            # Notify callback if provided
            if self.device_change_callback:
                try:
                    self.device_change_callback(new_device)
                except Exception as e:
                    print(f"Error in device change callback: {e}")

            return True

        return False

    def get_device_info(self) -> Optional[Dict]:
        """Get current loopback device info."""
        return self.current_loopback_device

    def is_available(self) -> bool:
        """Check if system audio capture is available."""
        return self.wasapi_available and self.current_loopback_device is not None

    def start_device_monitoring(self):
        """Start monitoring for device changes (Bluetooth connections/disconnections)."""
        if self.is_monitoring:
            return

        self.is_monitoring = True
        self.monitoring_thread = threading.Thread(
            target=self._monitor_device_changes,
            daemon=True
        )
        self.monitoring_thread.start()
        print("🔄 System audio device monitoring started (Bluetooth-aware)")

    def stop_device_monitoring(self):
        """Stop monitoring for device changes."""
        self.is_monitoring = False
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            self.monitoring_thread.join(timeout=1.0)
        print("⏹️ System audio device monitoring stopped")

    def _monitor_device_changes(self):
        """Monitor for audio device changes in background thread (optimized for Bluetooth)."""
        while self.is_monitoring:
            try:
                current_time = time.time()

                # Check for device changes more frequently for better Bluetooth detection
                if current_time - self.last_device_check >= self.device_check_interval:
                    self.last_device_check = current_time

                    # Check if default device has changed
                    new_device = self._get_default_loopback_device()

                    if new_device and self.current_loopback_device:
                        # Compare device indices to detect changes
                        if new_device['index'] != self.current_loopback_device['index']:
                            old_device_name = self.current_loopback_device['name']
                            new_device_name = new_device['name']

                            print(f"🔄 Audio device change detected:")
                            print(f"   From: {old_device_name}")
                            print(f"   To: {new_device_name}")

                            # Update current device
                            self.current_loopback_device = new_device

                            # Restart stream if currently capturing
                            if self.is_capturing and self.stream:
                                print("🔄 Restarting audio stream for new device...")
                                self._restart_stream_for_new_device()

                            # Notify callback
                            if self.device_change_callback:
                                try:
                                    self.device_change_callback(new_device)
                                except Exception as e:
                                    print(f"Error in device change callback: {e}")

                    elif new_device and not self.current_loopback_device:
                        # Device became available
                        self.current_loopback_device = new_device
                        print(f"✅ Audio device became available: {new_device['name']}")

                        if self.device_change_callback:
                            try:
                                self.device_change_callback(new_device)
                            except Exception as e:
                                print(f"Error in device change callback: {e}")

                time.sleep(0.5)  # Check twice per second for responsive Bluetooth detection

            except Exception as e:
                print(f"Error in device monitoring: {e}")
                time.sleep(2.0)  # Longer sleep on error

    def _restart_stream_for_new_device(self):
        """Restart audio stream for new device without interrupting the application."""
        try:
            self.device_switch_in_progress = True

            # Close current stream
            if self.stream:
                self.stream.stop_stream()
                self.stream.close()
                self.stream = None

            # Small delay to ensure device is ready
            time.sleep(0.2)

            # Reopen stream with new device
            if self.open_stream():
                print("✅ Audio stream successfully switched to new device")
            else:
                print("❌ Failed to switch to new device")

        except Exception as e:
            print(f"Error restarting stream for new device: {e}")
        finally:
            self.device_switch_in_progress = False

    def cleanup(self):
        """Clean up resources."""
        self.stop_device_monitoring()
        self.close_stream()
        if hasattr(self, 'audio') and self.audio:
            self.audio.terminate()

# Voice Activity Detection and Audio Quality Assessment Classes
class VoiceActivityDetector:
    """
    Advanced Voice Activity Detection to distinguish speech from background noise
    """

    def __init__(self, sample_rate=16000):
        self.sample_rate = sample_rate
        self.energy_threshold = 0.01  # Will be adaptive
        self.spectral_threshold = 1000  # Hz
        self.min_speech_duration = 0.5  # seconds
        self.background_noise_level = 0.001
        self.adaptation_rate = 0.1

        # Speech detection state
        self.speech_frames = 0
        self.required_speech_frames = int(self.min_speech_duration * sample_rate / 1024)

    def calculate_energy(self, audio_chunk):
        """Calculate RMS energy of audio chunk"""
        try:
            audio_data = np.frombuffer(audio_chunk, dtype=np.int16)
            if len(audio_data) == 0:
                return 0.0

            # Normalize to [-1, 1] range
            audio_data = audio_data.astype(np.float32) / 32768.0

            # Calculate RMS energy
            rms_energy = np.sqrt(np.mean(audio_data ** 2))
            return rms_energy
        except Exception:
            return 0.0

    def calculate_spectral_centroid(self, audio_chunk):
        """Calculate spectral centroid to detect speech characteristics"""
        try:
            audio_data = np.frombuffer(audio_chunk, dtype=np.int16)
            if len(audio_data) < 512:
                return 0.0

            # Simple FFT-based spectral centroid
            fft = np.fft.rfft(audio_data)
            magnitude = np.abs(fft)
            freqs = np.fft.rfftfreq(len(audio_data), 1/self.sample_rate)

            # Calculate centroid
            if np.sum(magnitude) > 0:
                centroid = np.sum(freqs * magnitude) / np.sum(magnitude)
                return centroid
            return 0.0
        except Exception:
            return 0.0

    def is_speech(self, audio_chunk):
        """
        Determine if audio chunk contains speech
        Returns: (is_speech, confidence, energy_level)
        """
        try:
            energy = self.calculate_energy(audio_chunk)
            spectral_centroid = self.calculate_spectral_centroid(audio_chunk)

            # Adapt background noise level
            if energy < self.energy_threshold * 0.5:
                self.background_noise_level = (
                    self.background_noise_level * (1 - self.adaptation_rate) +
                    energy * self.adaptation_rate
                )

            # Adaptive energy threshold
            adaptive_threshold = max(
                self.energy_threshold,
                self.background_noise_level * 3.0
            )

            # Speech detection criteria
            energy_check = energy > adaptive_threshold
            spectral_check = 300 < spectral_centroid < 4000  # Typical speech range

            # Calculate confidence
            energy_confidence = min(1.0, energy / adaptive_threshold)
            spectral_confidence = 1.0 if spectral_check else 0.3

            overall_confidence = (energy_confidence + spectral_confidence) / 2.0

            # Require sustained speech
            if energy_check and spectral_check:
                self.speech_frames += 1
                is_sustained_speech = self.speech_frames >= self.required_speech_frames
            else:
                self.speech_frames = max(0, self.speech_frames - 2)  # Decay faster
                is_sustained_speech = False

            return is_sustained_speech, overall_confidence, energy

        except Exception:
            return False, 0.0, 0.0

    def reset(self):
        """Reset speech detection state"""
        self.speech_frames = 0

class TriggerPhraseDetector:
    """
    Detects trigger phrases that indicate interview questions
    """

    def __init__(self):
        self.trigger_phrases = [
            # Direct question indicators
            "interview question",
            "technical question",
            "can you explain",
            "what is",
            "how does",
            "tell me about",
            "describe",
            "explain",

            # Scenario-based indicators
            "imagine you",
            "suppose you",
            "in a project",
            "if you were",
            "how would you",
            "what would you do",
            "consider a scenario",
            "let's say",

            # Technical context indicators
            "in react",
            "in angular",
            "in javascript",
            "in node",
            "in database",
            "in frontend",
            "in backend",
            "performance",
            "optimization",
            "security",
            "scalability"
        ]

        self.question_words = [
            "what", "how", "why", "when", "where", "which", "who",
            "explain", "describe", "tell", "show", "demonstrate"
        ]

    def detect_trigger(self, text):
        """
        Detect if text contains interview question triggers
        Returns: (is_trigger, confidence, detected_phrases)
        """
        if not text:
            return False, 0.0, []

        text_lower = text.lower()
        detected_phrases = []
        confidence_score = 0.0

        # Check for trigger phrases
        for phrase in self.trigger_phrases:
            if phrase in text_lower:
                detected_phrases.append(phrase)
                confidence_score += 0.3

        # Check for question words at the beginning
        words = text_lower.split()
        if words and words[0] in self.question_words:
            confidence_score += 0.4
            detected_phrases.append(f"question_word: {words[0]}")

        # Check for question marks
        if "?" in text:
            confidence_score += 0.2
            detected_phrases.append("question_mark")

        # Check for technical keywords
        technical_keywords = [
            "component", "function", "variable", "array", "object",
            "api", "database", "server", "client", "framework",
            "library", "algorithm", "data structure", "design pattern"
        ]

        for keyword in technical_keywords:
            if keyword in text_lower:
                confidence_score += 0.1
                detected_phrases.append(f"technical: {keyword}")

        # Normalize confidence
        confidence_score = min(1.0, confidence_score)

        # Consider it a trigger if confidence > 0.3
        is_trigger = confidence_score > 0.3

        return is_trigger, confidence_score, detected_phrases

class QuestionModeManager:
    """
    Manages different modes for question processing
    """

    def __init__(self):
        self.mode = "IDLE"  # IDLE, TRIGGERED, PROCESSING, RESPONDING
        self.trigger_time = None
        self.trigger_timeout = 10.0  # seconds
        self.manual_activation = False

    def set_mode(self, new_mode):
        """Set the current mode"""
        old_mode = self.mode
        self.mode = new_mode

        if new_mode == "TRIGGERED":
            self.trigger_time = time.time()
        elif new_mode == "IDLE":
            self.trigger_time = None
            self.manual_activation = False

        return old_mode != new_mode  # Return True if mode changed

    def get_mode(self):
        """Get current mode"""
        return self.mode

    def is_triggered_expired(self):
        """Check if triggered mode has expired"""
        if self.mode == "TRIGGERED" and self.trigger_time:
            return time.time() - self.trigger_time > self.trigger_timeout
        return False

    def activate_manually(self):
        """Manually activate question mode"""
        self.manual_activation = True
        self.set_mode("TRIGGERED")

    def should_process_audio(self):
        """Determine if audio should be processed"""
        return self.mode in ["TRIGGERED", "PROCESSING"] or self.manual_activation

# API Configuration (same as ANSARI_AI_V2)
API_KEYS = {
    'gemini': 'AIzaSyDkzI3sSS2q4pC-hBnGqfszGii1qh2lOjg',
    'mistral': 'p7KFyMJyUNKqwNDUX4kdKwEEpc5eTAA1',
    'openrouter': '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',
    'openai': 'sk-1234567890abcdef'
}

API_ENDPOINTS = {
    'gemini': 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent',
    'mistral': 'https://api.mistral.ai/v1/chat/completions',
    'openrouter': 'https://openrouter.ai/api/v1/chat/completions',
    'openai': 'https://api.openai.com/v1/chat/completions'
}

# Enhanced System Prompt for Technical Interview Assistant with Scenario Understanding
SYSTEM_PROMPT = """You are an experienced Full Stack Developer expert giving technical interview answers. You excel at understanding both direct and indirect/scenario-based questions.

IMPORTANT: When someone asks a question in a roundabout way or through a scenario, identify the core technical concept they're asking about and provide a comprehensive answer.

Examples of indirect questions you should understand:
- "In a project where users complain about slow loading..." → They're asking about performance optimization
- "If I have data that needs to be shared between components..." → They're asking about state management
- "When building an app that needs to work offline..." → They're asking about PWA/offline strategies
- "Imagine you're building an e-commerce site and need to handle payments..." → They're asking about payment integration

RESPONSE FORMAT:
**Technical Concept:** [Identify the main concept being asked about]

**Definition:** [Clear explanation in simple English, 4-5 lines]

**Practical Example:**
```javascript
// Clean, well-commented code example
// that demonstrates the concept clearly
```

**Key Points:**
- [2-3 bullet points with important details]

GUIDELINES:
- Always identify the core technical concept first
- Provide practical, real-world examples
- Use simple English but be technically accurate
- Focus on interview-relevant information
- If the question is unclear, provide the most likely technical interpretation"""

# Stealth Configuration
WINDOW_TRANSPARENCY = 0.90
ENABLE_SCREEN_CAPTURE_HIDING = True
ENABLE_TASKBAR_HIDING = False
LOCAL_VISIBILITY_MODE = True
DEBUG_STEALTH_FEATURES = False

class APIClient:
    def __init__(self):
        self.session = requests.Session()
        # Optimize session for faster connections and lower latency
        self.session.headers.update({
            'Connection': 'keep-alive',
            'User-Agent': 'InterV-AI/1.0',
            'Accept-Encoding': 'gzip, deflate',  # Enable compression
            'Cache-Control': 'no-cache'  # Prevent caching delays
        })
        # Enhanced connection pooling for faster requests
        adapter = requests.adapters.HTTPAdapter(
            pool_connections=3,      # More connections for parallel requests
            pool_maxsize=10,         # Larger pool for better reuse
            max_retries=1,           # Quick retry for failed requests
            pool_block=False         # Non-blocking pool for faster response
        )
        self.session.mount('http://', adapter)
        self.session.mount('https://', adapter)
        self.last_used_model = "Unknown"

        # Performance tracking
        self.response_times = []
        self.last_response_time = 0

        # Pre-warm connections for faster first request
        self._prewarm_connections()

    def get_performance_stats(self):
        """Get API performance statistics."""
        if not self.response_times:
            return "No API calls made yet"

        avg_time = sum(self.response_times) / len(self.response_times)
        min_time = min(self.response_times)
        max_time = max(self.response_times)

        return f"API Performance: Avg: {avg_time:.2f}s, Min: {min_time:.2f}s, Max: {max_time:.2f}s ({len(self.response_times)} calls)"

    def _prewarm_connections(self):
        """Pre-warm connections to API endpoints for faster first request"""
        def prewarm_endpoint(url):
            try:
                # Make a quick HEAD request to establish connection
                self.session.head(url, timeout=2)
            except Exception:
                pass  # Silently fail if pre-warming doesn't work

        # Pre-warm connections in background thread to avoid blocking startup
        import threading
        threading.Thread(target=lambda: [
            prewarm_endpoint("https://generativelanguage.googleapis.com"),
            prewarm_endpoint("https://api.mistral.ai"),
            prewarm_endpoint("https://openrouter.ai")
        ], daemon=True).start()

    def get_ai_response(self, text):
        """Get AI response with parallel processing for maximum speed"""
        # Try parallel approach first for fastest response
        try:
            result = self._get_ai_response_parallel(text)
            if result:
                return result
        except Exception as e:
            print(f"Parallel API call failed: {e}")

        # Fallback to sequential approach if parallel fails
        return self._get_ai_response_sequential(text)

    def _get_ai_response_parallel(self, text):
        """Get AI response using parallel API calls for maximum speed"""
        import concurrent.futures

        # Create focused prompt for technical definitions
        full_prompt = f"{SYSTEM_PROMPT}\n\nTechnical Question: {text}\n\nProvide a clear, concise answer with definition and example:"
        start_time = time.time()

        def try_gemini():
            try:
                api_key = API_KEYS.get('gemini')
                if not api_key:
                    return None

                payload = {
                    "contents": [{"parts": [{"text": full_prompt}]}],
                    "generationConfig": {
                        "temperature": 0.2,      # Lower temperature for faster, more focused responses
                        "maxOutputTokens": 400,  # Reduced for faster response
                        "topK": 1,              # Single choice for maximum speed
                        "topP": 0.7,            # More focused probability for speed
                        "candidateCount": 1,    # Single candidate for speed
                        "stopSequences": []     # No stop sequences for faster processing
                    }
                }

                headers = {"Content-Type": "application/json"}
                url = f"{API_ENDPOINTS['gemini']}?key={api_key}"

                response = self.session.post(url, data=json.dumps(payload), headers=headers, timeout=3)
                response.raise_for_status()

                result = response.json()
                if 'candidates' in result and result['candidates']:
                    return ("Gemini", result['candidates'][0]['content']['parts'][0]['text'])
            except Exception:
                return None

        def try_mistral():
            try:
                api_key = API_KEYS.get('mistral')
                if not api_key:
                    return None

                payload = {
                    "model": "mistral-large-latest",
                    "messages": [
                        {"role": "system", "content": SYSTEM_PROMPT},
                        {"role": "user", "content": text}
                    ],
                    "temperature": 0.5,  # Optimized for speed
                    "max_tokens": 400    # Reduced for faster response
                }

                headers = {"Authorization": f"Bearer {api_key}", "Content-Type": "application/json"}
                response = self.session.post(API_ENDPOINTS['mistral'], data=json.dumps(payload), headers=headers, timeout=4)
                response.raise_for_status()

                result = response.json()
                if 'choices' in result and result['choices']:
                    return ("Mistral", result['choices'][0]['message']['content'])
            except Exception:
                return None

        # Execute API calls in parallel
        with concurrent.futures.ThreadPoolExecutor(max_workers=2) as executor:
            # Submit both API calls simultaneously
            future_gemini = executor.submit(try_gemini)
            future_mistral = executor.submit(try_mistral)

            # Wait for first successful response
            for future in concurrent.futures.as_completed([future_gemini, future_mistral], timeout=5):
                try:
                    result = future.result()
                    if result:
                        model_name, response_text = result
                        self.last_used_model = model_name
                        response_time = time.time() - start_time
                        self.last_response_time = response_time
                        self.response_times.append(response_time)
                        print(f"⚡ {model_name} parallel response time: {response_time:.2f}s")

                        # Cancel remaining futures for efficiency
                        future_gemini.cancel()
                        future_mistral.cancel()

                        return response_text
                except Exception:
                    continue

        return None

    def _get_ai_response_sequential(self, text):
        """Fallback sequential API calls (original implementation with optimizations)"""
        # Create focused prompt for technical definitions
        full_prompt = f"{SYSTEM_PROMPT}\n\nTechnical Question: {text}\n\nProvide a clear, concise answer with definition and example:"

        # Try Gemini first (optimized for speed)
        start_time = time.time()
        try:
            api_key = API_KEYS.get('gemini')
            if api_key:
                payload = {
                    "contents": [{"parts": [{"text": full_prompt}]}],
                    "generationConfig": {
                        "temperature": 0.2,      # Lower temperature for faster, more focused responses
                        "maxOutputTokens": 400,  # Reduced for faster response
                        "topK": 1,              # Single choice for maximum speed
                        "topP": 0.7,            # More focused probability for speed
                        "candidateCount": 1,    # Single candidate for speed
                        "stopSequences": []     # No stop sequences for faster processing
                    }
                }

                headers = {"Content-Type": "application/json"}
                url = f"{API_ENDPOINTS['gemini']}?key={api_key}"

                response = self.session.post(url, data=json.dumps(payload), headers=headers, timeout=3)
                response.raise_for_status()

                result = response.json()
                if 'candidates' in result and result['candidates']:
                    self.last_used_model = "Gemini"
                    response_time = time.time() - start_time
                    self.last_response_time = response_time
                    self.response_times.append(response_time)
                    print(f"⚡ Gemini sequential response time: {response_time:.2f}s")
                    return result['candidates'][0]['content']['parts'][0]['text']
        except Exception as e:
            pass  # Try next model

        # Try OpenRouter as fallback (faster than Mistral)
        try:
            api_key = API_KEYS.get('openrouter')
            if api_key:
                payload = {
                    "model": "google/gemini-flash-1.5",
                    "messages": [
                        {"role": "system", "content": SYSTEM_PROMPT},
                        {"role": "user", "content": text}
                    ],
                    "temperature": 0.5,  # Optimized for speed
                    "max_tokens": 400    # Reduced for faster response
                }

                headers = {"Authorization": f"Bearer {api_key}", "Content-Type": "application/json"}
                response = self.session.post(API_ENDPOINTS['openrouter'], data=json.dumps(payload), headers=headers, timeout=4)
                response.raise_for_status()

                result = response.json()
                if 'choices' in result and result['choices']:
                    self.last_used_model = "OpenRouter"
                    response_time = time.time() - start_time
                    self.last_response_time = response_time
                    self.response_times.append(response_time)
                    print(f"⚡ OpenRouter sequential response time: {response_time:.2f}s")
                    return result['choices'][0]['message']['content']
        except Exception as e:
            pass

        # If all models fail
        self.last_used_model = "Error"
        return "All AI models are currently unavailable. Please try again later."

    def get_last_used_model(self):
        """Get the name of the last successfully used model"""
        return self.last_used_model

class AudioSignals(QThread):
    """Signals for audio processing"""
    text_updated = pyqtSignal(str)
    final_text = pyqtSignal(str)
    ai_response = pyqtSignal(str)

class RealTimeTranscriber(QThread):
    # Signals for GUI updates
    text_updated = pyqtSignal(str)
    final_text = pyqtSignal(str)
    ai_response = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        try:
            # Audio configuration (optimized for faster response while maintaining accuracy)
            self.CHUNK = 1024              # Optimal chunk size for real-time processing
            self.FORMAT = pyaudio.paInt16
            self.CHANNELS = 1
            self.RATE = 16000              # Whisper's native sample rate for best performance
            self.SILENCE_THRESHOLD = 150   # More sensitive for faster detection
            self.SILENCE_DURATION = 2.5    # Increased for better noise filtering
            self.CHUNK_DURATION = 0.7      # More frequent processing for lower latency
            self.MIN_AUDIO_LENGTH = 1.5    # Increased minimum audio length to filter noise

            # Initialize intelligent audio processing components
            self.vad = VoiceActivityDetector(sample_rate=self.RATE)
            self.trigger_detector = TriggerPhraseDetector()
            self.question_mode = QuestionModeManager()

            # Audio quality thresholds
            self.MIN_SPEECH_CONFIDENCE = 0.4
            self.MIN_TRIGGER_CONFIDENCE = 0.3

            # State tracking
            self.last_trigger_check = 0
            self.trigger_check_interval = 0.5  # Check for triggers every 500ms
            self.current_audio_quality = 0.0

            # Initialize System Audio Capture (WASAPI loopback) instead of microphone
            print("🔧 Initializing system audio capture...")
            self.system_audio = SystemAudioCapture(device_change_callback=self._on_device_change)

            # Fallback to traditional PyAudio if system audio not available
            if not self.system_audio.is_available():
                print("⚠️ System audio capture not available, falling back to microphone input")
                self.audio = pyaudio.PyAudio()
                self.device_manager = AudioDeviceManager(device_change_callback=self._on_device_change)
                self.use_system_audio = False
            else:
                print("✅ System audio capture initialized successfully")
                self.audio = None  # Will use system_audio instead
                self.device_manager = None
                self.use_system_audio = True

            # Initialize stream variables
            self.current_device_index = None
            self.stream = None
            self.device_switch_in_progress = False

            # Load Whisper model (optimized for speed vs accuracy balance)
            global WHISPER_TYPE
            if WHISPER_TYPE == "faster":
                # Faster-whisper with optimized settings for speed
                self.model = WhisperModel(
                    "base",  # Good balance of speed and accuracy
                    device="cpu",
                    compute_type="int8",  # Faster inference
                    num_workers=1,  # Single worker for lower latency
                    download_root=None,
                    local_files_only=False
                )
                self.whisper_type = "faster"
                print("✅ Faster-whisper model loaded with speed optimizations")
            else:
                # OpenAI Whisper (fallback)
                self.model = whisper.load_model("base")
                self.whisper_type = "openai"
                print("✅ OpenAI Whisper model loaded")

            # Audio buffer (in-memory only)
            self.audio_buffer = deque()
            self.is_recording = False
            self.last_sound_time = time.time()
            self.recording_start_time = None
            self.current_transcribed_text = ""

            # Threading
            self.stop_flag = threading.Event()
            self.chunk_counter = 0

            # API client for AI responses
            self.api_client = APIClient()

            # Start device monitoring (only for fallback mode)
            if not self.use_system_audio and self.device_manager:
                self.device_manager.start_monitoring()
                print("✅ Audio device manager initialized and monitoring started")
            elif self.use_system_audio:
                print("✅ System audio capture ready - no device monitoring needed")

        except Exception as e:
            # Silent fail for exe - just emit error signal
            self.error_occurred = True
            print(f"❌ Error initializing RealTimeTranscriber: {e}")

    def _on_device_change(self, old_device=None, new_device=None):
        """Handle automatic device changes for both system audio and microphone modes (optimized for Bluetooth)."""
        if hasattr(self, 'device_switch_in_progress') and self.device_switch_in_progress:
            return  # Avoid recursive calls

        self.device_switch_in_progress = True
        try:
            if self.use_system_audio:
                # For system audio, the SystemAudioCapture class handles device switching automatically
                device_name = new_device.get('name', 'Unknown') if isinstance(new_device, dict) else str(new_device)
                print(f"🔄 System audio device switched to: {device_name}")

                # Update audio settings if device capabilities changed
                if hasattr(self.system_audio, 'RATE'):
                    self.RATE = self.system_audio.RATE
                    print(f"   Updated sample rate: {self.RATE} Hz")

                # SystemAudioCapture handles stream restart automatically
                print("✅ Bluetooth device switch completed seamlessly")

            else:
                # For microphone mode (fallback)
                print(f"🔄 Microphone device changed: {old_device} -> {new_device}")
                # Update current device index
                self.current_device_index = new_device
                # Restart audio stream with new device if currently recording
                if self.is_recording and hasattr(self, 'stream') and self.stream:
                    self._restart_audio_stream()

        except Exception as e:
            print(f"❌ Error handling device change: {e}")
        finally:
            self.device_switch_in_progress = False

    def _restart_audio_stream(self):
        """Restart audio stream with current device."""
        try:
            # Close current stream
            if self.stream:
                self.stream.stop_stream()
                self.stream.close()
                self.stream = None

            # Small delay to allow device to stabilize
            time.sleep(0.5)

            # Get current device index
            device_index = self._get_current_device_index()
            if device_index is not None:
                self.stream = self.audio.open(
                    format=self.FORMAT,
                    channels=self.CHANNELS,
                    rate=self.RATE,
                    input=True,
                    frames_per_buffer=self.CHUNK,
                    input_device_index=device_index
                )
                print(f"✅ Audio stream restarted on device {device_index}")
            else:
                print("❌ No valid device found for restart")

        except Exception as e:
            print(f"❌ Error restarting audio stream: {e}")

    def _get_current_device_index(self):
        """Get the current device index using device manager (fallback mode only)."""
        if not self.use_system_audio and self.device_manager:
            if self.current_device_index is None:
                self.current_device_index = self.device_manager.get_system_default_input_device()
            return self.current_device_index
        return None

    def is_silent(self, audio_chunk):
        """Enhanced silence detection with voice activity detection"""
        # Traditional silence check
        audio_data = np.frombuffer(audio_chunk, dtype=np.int16)
        is_traditional_silent = np.max(np.abs(audio_data)) < self.SILENCE_THRESHOLD

        # Voice activity detection
        is_speech, confidence, energy = self.vad.is_speech(audio_chunk)
        self.current_audio_quality = confidence

        # Consider it silent if:
        # 1. Traditional silence detection says it's silent, OR
        # 2. VAD confidence is below threshold (likely background noise)
        is_intelligent_silent = is_traditional_silent or (confidence < self.MIN_SPEECH_CONFIDENCE)

        return is_intelligent_silent

    def should_process_audio_chunk(self, audio_chunk, transcribed_text=""):
        """
        Determine if audio chunk should be processed based on multiple criteria
        """
        # Check if we're in a mode that allows processing
        if not self.question_mode.should_process_audio():
            # In IDLE mode, only check for trigger phrases in transcribed text
            if transcribed_text:
                is_trigger, confidence, phrases = self.trigger_detector.detect_trigger(transcribed_text)
                if is_trigger and confidence > self.MIN_TRIGGER_CONFIDENCE:
                    print(f"🎯 Trigger detected: {phrases} (confidence: {confidence:.2f})")
                    self.question_mode.set_mode("TRIGGERED")
                    return True
            return False

        # Check if triggered mode has expired
        if self.question_mode.is_triggered_expired():
            print("⏰ Trigger mode expired, returning to IDLE")
            self.question_mode.set_mode("IDLE")
            self.vad.reset()
            return False

        # In triggered/processing mode, check audio quality
        is_speech, confidence, energy = self.vad.is_speech(audio_chunk)

        if confidence < self.MIN_SPEECH_CONFIDENCE:
            return False

        return True
    
    def process_chunk_realtime(self, audio_data):
        """Process audio chunk for real-time transcription (optimized for speed)"""
        try:
            # Skip processing if audio data is too small (performance optimization)
            if len(audio_data) < 1024:
                return

            # Create in-memory WAV file (faster than disk I/O)
            wav_buffer = io.BytesIO()
            with wave.open(wav_buffer, 'wb') as wav_file:
                wav_file.setnchannels(self.CHANNELS)
                # Get sample width based on format (2 bytes for paInt16)
                sample_width = 2 if self.FORMAT == pyaudio.paInt16 else 1
                wav_file.setsampwidth(sample_width)
                wav_file.setframerate(self.RATE)
                wav_file.writeframes(audio_data)

            # Reset buffer position for reading
            wav_buffer.seek(0)

            # Use unique temp file with faster naming
            global TEMP_DIR
            temp_chunk_file = os.path.join(TEMP_DIR, f"chunk_{self.chunk_counter}.wav")
            self.chunk_counter += 1

            # Write temp file (minimal I/O)
            with open(temp_chunk_file, 'wb') as f:
                f.write(wav_buffer.getvalue())

            # Transcribe with Whisper (optimized for speed)
            if self.whisper_type == "faster":
                # Optimized faster-whisper settings for speed
                segments, info = self.model.transcribe(
                    temp_chunk_file,
                    language="en",
                    beam_size=1,           # Faster beam search
                    best_of=1,             # Single candidate for speed
                    temperature=0.0,       # Deterministic for speed
                    compression_ratio_threshold=2.4,  # Skip low-quality audio faster
                    log_prob_threshold=-1.0,           # Less strict filtering
                    no_speech_threshold=0.6,           # Faster silence detection
                    condition_on_previous_text=False,  # Faster processing
                    initial_prompt=None,               # No prompt overhead
                    word_timestamps=False              # Skip word-level timestamps
                )
                text = " ".join([segment.text for segment in segments]).strip()
            else:
                # Optimized OpenAI Whisper settings
                result = self.model.transcribe(
                    temp_chunk_file,
                    language="en",
                    fp16=False,            # Faster on CPU
                    condition_on_previous_text=False  # Faster processing
                )
                text = result["text"].strip()

            # Immediate cleanup (non-blocking)
            try:
                os.remove(temp_chunk_file)
            except:
                pass

            if text and len(text.strip()) > 2:  # Only process meaningful text (optimized)
                # Update current transcribed text with smart comparison
                if len(text) > len(self.current_transcribed_text) or text != self.current_transcribed_text:
                    self.current_transcribed_text = text
                    # Emit signal to update GUI (non-blocking)
                    self.text_updated.emit(self.current_transcribed_text)

        except Exception as e:
            # Silent fail for exe
            pass

    def run(self):
        """Main thread execution for QThread"""
        self.record_audio()

    def record_audio(self):
        """Record audio from system output (WASAPI loopback) or microphone fallback"""
        try:
            if self.use_system_audio:
                # Use system audio capture (WASAPI loopback)
                self._record_system_audio()
            else:
                # Fallback to microphone input
                self._record_microphone_audio()
        except Exception as e:
            print(f"❌ Error in record_audio: {e}")

    def _record_system_audio(self):
        """Record audio from system output using WASAPI loopback"""
        try:
            if not self.system_audio.is_available():
                print("❌ System audio capture not available")
                return

            # Open system audio stream
            if not self.system_audio.open_stream():
                print("❌ Failed to open system audio stream")
                return

            # Update audio settings from system audio capture
            self.RATE = self.system_audio.RATE
            self.CHANNELS = 1  # Always use mono for transcription

            device_info = self.system_audio.get_device_info()
            print(f"🔊 Recording from system audio: {device_info['name']}")
            print(f"   Sample Rate: {self.RATE} Hz")
            print(f"   Format: {self.FORMAT}")

            # Main recording loop
            self._audio_recording_loop(use_system_audio=True)

        except Exception as e:
            print(f"❌ Error in system audio recording: {e}")
        finally:
            if hasattr(self, 'system_audio'):
                self.system_audio.close_stream()

    def _record_microphone_audio(self):
        """Fallback method: Record audio from microphone input"""
        try:
            # Get current device index using device manager
            device_index = self._get_current_device_index()
            if device_index is None:
                print("❌ No audio input device available")
                return

            print(f"🎤 Opening microphone stream on device {device_index}")
            device_info = self.device_manager.get_device_info(device_index)
            if device_info:
                print(f"🎤 Using device: {device_info['name']}")

            self.stream = self.audio.open(
                format=self.FORMAT,
                channels=self.CHANNELS,
                rate=self.RATE,
                input=True,
                frames_per_buffer=self.CHUNK,
                input_device_index=device_index
            )

            # Main recording loop
            self._audio_recording_loop(use_system_audio=False)

        except Exception as e:
            print(f"❌ Error in microphone recording: {e}")
        finally:
            if hasattr(self, 'stream') and self.stream:
                try:
                    self.stream.stop_stream()
                    self.stream.close()
                except Exception as e:
                    print(f"Error closing microphone stream: {e}")

    def _audio_recording_loop(self, use_system_audio: bool = True):
        """Unified audio recording loop for both system audio and microphone input"""
        chunk_buffer = []
        last_chunk_time = time.time()

        while not self.stop_flag.is_set():
            try:
                    # Read audio chunk based on mode
                    if use_system_audio:
                        # Read from system audio capture
                        audio_chunk = self.system_audio.read_audio_chunk()
                        if audio_chunk is None:
                            # Check if device switch is in progress
                            if hasattr(self.system_audio, 'device_switch_in_progress') and self.system_audio.device_switch_in_progress:
                                print("🔄 Device switch in progress, waiting...")
                                time.sleep(0.1)
                                continue

                            print("❌ System audio stream lost, attempting to reconnect...")
                            if self.system_audio.open_stream():
                                continue
                            else:
                                print("❌ Failed to reconnect system audio, stopping recording")
                                break
                    else:
                        # Read from microphone (fallback mode)
                        # Check if stream is still valid (device might have been disconnected)
                        if not self.stream:
                            print("❌ Audio stream lost, attempting to reconnect...")
                            device_index = self._get_current_device_index()
                            if device_index is not None:
                                self.stream = self.audio.open(
                                    format=self.FORMAT,
                                    channels=self.CHANNELS,
                                    rate=self.RATE,
                                    input=True,
                                    frames_per_buffer=self.CHUNK,
                                    input_device_index=device_index
                                )
                            else:
                                print("❌ No audio device available, stopping recording")
                                break

                        audio_chunk = self.stream.read(self.CHUNK, exception_on_overflow=False)

                    # Enhanced audio processing with intelligent filtering
                    if not self.is_silent(audio_chunk):
                        # Check if we should process this audio based on current mode and quality
                        should_process = self.should_process_audio_chunk(audio_chunk, self.current_transcribed_text)

                        if should_process:
                            if not self.is_recording:
                                self.is_recording = True
                                self.audio_buffer.clear()
                                chunk_buffer.clear()
                                self.current_transcribed_text = ""  # Reset text
                                self.recording_start_time = time.time()
                                last_chunk_time = time.time()

                                # Set mode to processing if triggered
                                if self.question_mode.get_mode() == "TRIGGERED":
                                    self.question_mode.set_mode("PROCESSING")
                                    print("🎤 Started processing interview question...")

                            self.audio_buffer.append(audio_chunk)
                            chunk_buffer.append(audio_chunk)
                            self.last_sound_time = time.time()

                            # Process chunk for real-time transcription every CHUNK_DURATION seconds
                            current_time = time.time()
                            if current_time - last_chunk_time >= self.CHUNK_DURATION and chunk_buffer:
                                # Use all audio from start for better accuracy
                                all_audio_data = b''.join(self.audio_buffer)
                                # Run transcription in separate thread to avoid blocking
                                threading.Thread(target=self.process_chunk_realtime, args=(all_audio_data,), daemon=True).start()
                                last_chunk_time = current_time
                        else:
                            # Audio doesn't meet quality/mode requirements
                            if self.is_recording:
                                # Stop recording if we were recording but audio quality dropped
                                print("⚠️ Audio quality insufficient, stopping recording")
                                self.is_recording = False

                    elif self.is_recording:
                        # Check if silence duration exceeded
                        if time.time() - self.last_sound_time > self.SILENCE_DURATION:
                            # Check if we have minimum audio length before processing
                            recording_duration = time.time() - self.recording_start_time
                            if recording_duration >= self.MIN_AUDIO_LENGTH:
                                self.process_final_audio()
                            else:
                                print(f"⚠️ Audio too short ({recording_duration:.1f}s), skipping transcription")

                            self.is_recording = False
                            self.current_transcribed_text = ""

            except Exception as e:
                print(f"Error reading audio chunk: {e}")
                continue
    
    def process_final_audio(self):
        """Process complete recorded audio with Whisper for final result"""
        if not self.audio_buffer:
            return

        try:
            # Combine all audio chunks
            audio_data = b''.join(self.audio_buffer)

            # Create temporary file in temp directory
            global TEMP_DIR
            temp_final_file = os.path.join(TEMP_DIR, f"final_audio_{int(time.time())}.wav")

            # Save to temporary WAV file
            with wave.open(temp_final_file, 'wb') as wav_file:
                wav_file.setnchannels(self.CHANNELS)
                # Get sample width based on format (2 bytes for paInt16)
                sample_width = 2 if self.FORMAT == pyaudio.paInt16 else 1
                wav_file.setsampwidth(sample_width)
                wav_file.setframerate(self.RATE)
                wav_file.writeframes(audio_data)

            # Transcribe with Whisper for final result (optimized for speed and accuracy)
            transcription_start = time.time()
            if self.whisper_type == "faster":
                # Optimized faster-whisper settings for final transcription
                segments, info = self.model.transcribe(
                    temp_final_file,
                    language="en",
                    beam_size=2,           # Slightly better accuracy for final result
                    best_of=1,             # Single candidate for speed
                    temperature=0.0,       # Deterministic
                    compression_ratio_threshold=2.4,
                    log_prob_threshold=-1.0,
                    no_speech_threshold=0.6,
                    condition_on_previous_text=False,
                    initial_prompt=None,
                    word_timestamps=False
                )
                transcribed_text = " ".join([segment.text for segment in segments]).strip()
            else:
                # Optimized OpenAI Whisper settings for final transcription
                result = self.model.transcribe(
                    temp_final_file,
                    language="en",
                    fp16=False,
                    condition_on_previous_text=False
                )
                transcribed_text = result["text"].strip()

            transcription_time = time.time() - transcription_start
            print(f"⚡ Transcription time: {transcription_time:.2f}s")

            # Immediately clean up temporary file
            try:
                os.remove(temp_final_file)
            except:
                pass

            if transcribed_text:
                # Enhanced context analysis for interview questions
                processed_text = self.analyze_and_enhance_question(transcribed_text)

                # Emit final text signal
                self.final_text.emit(transcribed_text)

                # Set mode to responding
                self.question_mode.set_mode("RESPONDING")

                # Get AI response in separate thread with enhanced context
                threading.Thread(target=self.get_ai_response_async, args=(processed_text,), daemon=True).start()

            # Reset to IDLE mode after processing
            self.question_mode.set_mode("IDLE")
            self.vad.reset()

        except Exception as e:
            # Silent fail for exe
            pass

    def analyze_and_enhance_question(self, transcribed_text):
        """
        Analyze transcribed text and enhance it for better AI understanding
        """
        try:
            # Check if it's a trigger phrase detection
            is_trigger, confidence, phrases = self.trigger_detector.detect_trigger(transcribed_text)

            if not is_trigger:
                # If no clear trigger, try to extract technical context
                enhanced_text = self.extract_technical_context(transcribed_text)
            else:
                enhanced_text = transcribed_text

            # Add context hints for the AI
            context_hints = []

            # Detect scenario-based questions
            scenario_indicators = [
                "imagine", "suppose", "consider", "let's say", "if you were",
                "in a project", "when building", "how would you"
            ]

            if any(indicator in transcribed_text.lower() for indicator in scenario_indicators):
                context_hints.append("This is a scenario-based interview question.")

            # Detect technical domain
            domains = {
                "frontend": ["react", "angular", "vue", "javascript", "css", "html", "component", "ui", "ux"],
                "backend": ["node", "express", "api", "server", "database", "sql", "mongodb"],
                "performance": ["slow", "fast", "optimize", "performance", "speed", "loading"],
                "security": ["secure", "security", "authentication", "authorization", "encrypt"],
                "architecture": ["design", "pattern", "structure", "scalable", "microservice"]
            }

            detected_domains = []
            text_lower = transcribed_text.lower()

            for domain, keywords in domains.items():
                if any(keyword in text_lower for keyword in keywords):
                    detected_domains.append(domain)

            if detected_domains:
                context_hints.append(f"Technical domains: {', '.join(detected_domains)}")

            # Combine original text with context hints
            if context_hints:
                enhanced_text = f"{enhanced_text}\n\nContext: {' '.join(context_hints)}"

            return enhanced_text

        except Exception as e:
            print(f"Error in context analysis: {e}")
            return transcribed_text

    def extract_technical_context(self, text):
        """
        Extract technical context from conversational text
        """
        # Common conversational patterns that indicate technical questions
        patterns = {
            "performance": ["slow", "fast", "speed", "optimize", "performance", "loading"],
            "state management": ["share data", "pass data", "state", "props", "context"],
            "component communication": ["communicate", "pass", "share", "between components"],
            "routing": ["navigate", "page", "route", "url", "navigation"],
            "api integration": ["api", "fetch", "request", "data", "server", "backend"],
            "database": ["store", "save", "database", "data persistence", "sql"],
            "authentication": ["login", "user", "auth", "permission", "access"],
            "deployment": ["deploy", "production", "build", "hosting", "server"]
        }

        text_lower = text.lower()
        detected_concepts = []

        for concept, keywords in patterns.items():
            if any(keyword in text_lower for keyword in keywords):
                detected_concepts.append(concept)

        if detected_concepts:
            # Add technical context to help AI understand
            context_note = f"[Technical concepts detected: {', '.join(detected_concepts)}] "
            return context_note + text

        return text

    def activate_question_mode_manually(self):
        """
        Manually activate question mode (can be called via keyboard shortcut)
        """
        self.question_mode.activate_manually()
        print("🎯 Question mode activated manually - ready for interview question!")
        return True

    def get_ai_response_async(self, text):
        """Get AI response asynchronously with streaming chunks"""
        try:
            response = self.api_client.get_ai_response(text)
            model_name = self.api_client.get_last_used_model()

            # Start streaming response in chunks
            self.stream_response_chunks(response, model_name)

        except Exception as e:
            self.ai_response.emit(f"Error: {str(e)}|MODEL:Error")

    def stream_response_chunks(self, full_response, model_name):
        """Stream response in chunks to simulate real-time typing"""
        # Run streaming in separate thread to avoid blocking
        threading.Thread(target=self._stream_chunks_worker, args=(full_response, model_name), daemon=True).start()

    def _stream_chunks_worker(self, full_response, model_name):
        """Worker thread for streaming chunks (optimized for immediate visibility)"""
        # Split response into words for natural chunking
        words = full_response.split()
        chunk_size = 4  # Show 4 words at a time for faster display

        # Start with empty response
        current_text = ""

        # Emit model name first
        self.ai_response.emit(f"|MODEL:{model_name}")

        # Stream words in chunks with optimized timing for immediate response
        for i in range(0, len(words), chunk_size):
            chunk_words = words[i:i + chunk_size]
            current_text += " ".join(chunk_words) + " "

            # Emit current accumulated text
            self.ai_response.emit(f"{current_text.strip()}|MODEL:{model_name}")

            # Optimized streaming for immediate visibility
            if i < 8:  # First 2 chunks instantly for immediate feedback
                time.sleep(0.015)  # 15ms for immediate start
            elif i < 16:  # Next chunks very fast
                time.sleep(0.03)   # 30ms for quick progression
            else:
                time.sleep(0.06)   # 60ms for remaining chunks
    
    def start_transcription(self):
        """Start the real-time transcription thread"""
        if not self.isRunning():
            self.stop_flag.clear()
            self.start()

    def stop_transcription(self):
        """Stop the transcription with proper cleanup"""
        print("🔄 Stopping transcription...")

        # Set stop flag first
        self.stop_flag.set()

        # Stop system audio capture if active
        if hasattr(self, 'system_audio') and self.system_audio:
            try:
                self.system_audio.close_stream()
                self.system_audio.stop_device_monitoring()
                self.system_audio.cleanup()
            except:
                pass

        # Stop device monitoring
        if hasattr(self, 'device_manager'):
            try:
                self.device_manager.cleanup()
            except:
                pass

        # Close audio stream
        if hasattr(self, 'stream') and self.stream:
            try:
                self.stream.stop_stream()
                self.stream.close()
                self.stream = None
            except:
                pass

        # Terminate audio
        try:
            if hasattr(self, 'audio'):
                self.audio.terminate()
        except:
            pass

        # Wait for thread to finish with timeout
        if self.isRunning():
            print("🔄 Waiting for transcription thread to finish...")
            self.wait(5000)  # Wait up to 5 seconds
            if self.isRunning():
                print("⚠️ Force terminating transcription thread...")
                self.terminate()

        # Clean up any remaining temp files
        self.cleanup_temp_files()

        print("✅ Transcription stopped successfully")

    def cleanup_temp_files(self):
        """Clean up temporary audio files"""
        try:
            global TEMP_DIR, AUDIO_BUFFERS
            AUDIO_BUFFERS.clear()

            if TEMP_DIR and os.path.exists(TEMP_DIR):
                # Clean all wav files in temp directory
                for file in os.listdir(TEMP_DIR):
                    if file.endswith('.wav'):
                        try:
                            os.remove(os.path.join(TEMP_DIR, file))
                        except:
                            pass
        except:
            pass

    def get_performance_stats(self):
        """Get comprehensive performance statistics."""
        stats = []

        # API performance
        if hasattr(self.api_client, 'get_performance_stats'):
            stats.append(self.api_client.get_performance_stats())

        # Audio processing stats
        if hasattr(self, 'chunk_counter'):
            stats.append(f"Audio chunks processed: {self.chunk_counter}")

        # Device info
        if self.use_system_audio and self.system_audio:
            device_info = self.system_audio.get_device_info()
            if device_info:
                stats.append(f"Current device: {device_info['name']}")
                stats.append(f"Sample rate: {self.RATE} Hz")

        return "\n".join(stats) if stats else "No performance data available"

class LiveTranscriptionApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.transcriber = None

        # Initialize Firebase authentication
        self.firebase_manager = FirebaseManager()
        self.current_user_email = None
        self.remaining_time = 0
        self.user_data = None
        self.session_start_time = None

        # Show authentication dialog first
        print("🔄 Starting user authentication...")
        auth_result = self.authenticate_user()
        print(f"🔄 Authentication result: {auth_result}")

        if not auth_result:
            print("❌ Authentication failed, exiting...")
            sys.exit()
            return

        print("✅ Authentication successful, initializing UI...")
        self.init_ui()
        self.setup_keyboard_shortcuts()
        self.apply_stealth_features()

        # Start time tracking and countdown
        self.setup_time_tracking()

        # Safe UI optimizations (non-breaking)
        self.setAttribute(Qt.WA_OpaquePaintEvent, True)
        self.setAttribute(Qt.WA_NoSystemBackground, False)

        # Setup always-on-top enforcement
        self.setup_always_on_top_enforcement()

        print("✅ Main application initialized successfully")

    def authenticate_user(self):
        """Show authentication dialog and handle user login"""
        try:
            print("🔄 Starting authentication dialog...")

            auth_dialog = UserAuthDialog(self)
            auth_successful = False

            def on_user_authenticated(email, remaining_time, user_data):
                nonlocal auth_successful
                try:
                    print(f"✅ Signal received - User authenticated: {email}, time: {remaining_time}s")
                    self.current_user_email = email
                    self.remaining_time = remaining_time
                    self.user_data = user_data
                    self.session_start_time = datetime.now()
                    auth_successful = True
                    print("✅ Authentication data saved successfully")
                except Exception as e:
                    print(f"❌ Error saving auth data: {e}")
                    import traceback
                    traceback.print_exc()

            # Connect signal
            print("🔄 Connecting authentication signal...")
            auth_dialog.user_authenticated.connect(on_user_authenticated)
            print("✅ Signal connected successfully")

            # Show dialog
            print("🔄 Showing authentication dialog...")
            result = auth_dialog.exec_()

            print(f"🔄 Dialog result: {result}, auth_successful: {auth_successful}")
            print(f"🔄 Dialog.Accepted = {auth_dialog.Accepted}")

            if result == auth_dialog.Accepted and auth_successful:
                print("✅ Authentication completed successfully")
                return True
            else:
                print(f"❌ Authentication failed or cancelled - result: {result}, success: {auth_successful}")
                return False

        except Exception as e:
            print(f"❌ Authentication error: {e}")
            import traceback
            traceback.print_exc()
            return False

    def setup_time_tracking(self):
        """Setup time tracking and title updates"""
        self.time_update_timer = QTimer()
        self.time_update_timer.timeout.connect(self.update_time_display)
        self.time_update_timer.start(1000)  # Update every second (safe frequency)

    def update_time_display(self):
        """Update window title with remaining time"""
        try:
            if self.session_start_time:
                # Calculate elapsed time
                elapsed = (datetime.now() - self.session_start_time).total_seconds()
                current_remaining = max(0, self.remaining_time - elapsed)

                # Convert to hours, minutes, seconds for better display
                hours = int(current_remaining // 3600)
                minutes = int((current_remaining % 3600) // 60)
                seconds = int(current_remaining % 60)

                # Update UI time display in H:M:S format
                if current_remaining > 0:
                    time_str = f"({hours:02d}:{minutes:02d}:{seconds:02d})"

                    # Update UI label if it exists
                    if hasattr(self, 'time_display_label'):
                        self.time_display_label.setText(time_str)

                    # Update window title (simplified)
                    self.setWindowTitle("InterV AI - Full Stack Developer")
                else:
                    # Time expired
                    if hasattr(self, 'time_display_label'):
                        self.time_display_label.setText("(00:00:00)")
                        self.time_display_label.setStyleSheet("color: #F44336; font-weight: bold; font-size: 12px;")

                    self.setWindowTitle("InterV AI - Full Stack Developer")

                    # Logout user
                    if not hasattr(self, '_logout_triggered'):
                        self._logout_triggered = True
                        self.logout_user()

        except Exception as e:
            pass

    def logout_user(self):
        """Logout user when time expires"""
        try:
            print("🔄 Logging out user - time expired...")

            # Stop time tracking timer
            if hasattr(self, 'time_update_timer'):
                self.time_update_timer.stop()

            # Update Firebase with logout time
            if hasattr(self, 'firebase_manager') and self.current_user_email:
                try:
                    user_ref = self.firebase_manager.db.collection('users').document(self.current_user_email)
                    user_ref.update({
                        'last_logout': firestore.SERVER_TIMESTAMP
                    })
                    print("✅ Updated logout time in Firebase")
                except Exception as e:
                    print(f"⚠️ Could not update logout time: {e}")

            # Show logout message
            from PyQt5.QtWidgets import QMessageBox
            msg = QMessageBox(self)
            msg.setWindowTitle("Session Expired")
            msg.setText("Your session time has expired.\nThe application will now close.")
            msg.setIcon(QMessageBox.Information)
            msg.setStandardButtons(QMessageBox.Ok)
            msg.setWindowFlags(msg.windowFlags() | Qt.WindowStaysOnTopHint)
            msg.exec_()

            # Close application
            print("✅ Closing application due to time expiry...")
            QApplication.quit()

        except Exception as e:
            print(f"❌ Error during logout: {e}")
            QApplication.quit()  # Force close on error

    def init_ui(self):
        """Initialize the user interface"""
        # Initial title - will be updated by time tracking
        self.setWindowTitle("InterV AI - Full Stack Developer Assistant")
        self.setGeometry(100, 100, 450, 650)

        # Set window flags for stealth with enhanced always-on-top behavior
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)

        # Additional flag to ensure window stays on top
        self.setAttribute(Qt.WA_AlwaysShowToolTips, True)
        self.setAttribute(Qt.WA_ShowWithoutActivating, False)  # Allow activation to maintain focus

        # Set window transparency
        self.setWindowOpacity(WINDOW_TRANSPARENCY)

        # Main widget and layout
        main_widget = QWidget()
        main_widget.setStyleSheet("background-color: white;")
        self.setCentralWidget(main_widget)
        layout = QVBoxLayout(main_widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Title bar with close button
        title_frame = QFrame()
        title_frame.setFixedHeight(35)
        title_frame.setStyleSheet("background-color: #f8f9fa; border: 1px solid #e9ecef; border-radius: 5px;")
        title_layout = QHBoxLayout(title_frame)
        title_layout.setContentsMargins(10, 5, 10, 5)

        title_label = QLabel("InterV AI - Full Stack Developer 👨‍💻")
        title_label.setStyleSheet("color: #333; font-weight: bold; font-size: 12px;")
        title_label.mousePressEvent = self.title_mouse_press
        title_layout.addWidget(title_label)

        # Time display label
        self.time_display_label = QLabel("")
        self.time_display_label.setStyleSheet("color: #4CAF50; font-weight: bold; font-size: 12px;")
        title_layout.addWidget(self.time_display_label)

        close_btn = QPushButton("✕")
        close_btn.setFixedSize(25, 25)
        close_btn.setStyleSheet("background-color: #dc3545; color: white; border: none; border-radius: 12px; font-weight: bold;")
        close_btn.clicked.connect(self.close)
        title_layout.addWidget(close_btn)

        layout.addWidget(title_frame)

        # Question section - Fixed at top (non-scrollable)
        question_frame = QFrame()
        question_frame.setFixedHeight(120)  # Fixed height to prevent expansion
        question_frame.setStyleSheet("background-color: white;")
        question_layout = QVBoxLayout(question_frame)
        question_layout.setContentsMargins(0, 0, 0, 5)
        question_layout.setSpacing(5)

        question_label = QLabel("🎤 Interview Question:")
        question_label.setStyleSheet("color: #333; font-weight: bold; font-size: 12px;")
        question_layout.addWidget(question_label)

        self.question_text = QLabel()
        # Make it read-only display with proper text wrapping
        self.question_text.setWordWrap(True)
        self.question_text.setAlignment(Qt.AlignTop | Qt.AlignLeft)
        self.question_text.setMinimumHeight(30)
        self.question_text.setMaximumHeight(80)  # Reduced to fit in fixed frame

        self.question_text.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 10px;
                font-size: 13px;
                color: #333;
                font-weight: normal;
            }
        """)
        self.question_text.setText("Speak your technical question and it will appear here...")
        question_layout.addWidget(self.question_text)

        layout.addWidget(question_frame)

        # Response section - Expandable
        self.response_label = QLabel("💡 Developer Answer:")
        self.response_label.setStyleSheet("color: #333; font-weight: bold; font-size: 12px;")
        layout.addWidget(self.response_label)

        self.response_text = QTextEdit()
        # Remove fixed height to allow expansion, but set minimum height
        self.response_text.setMinimumHeight(300)  # Minimum height for usability
        # Set size policy to expand both horizontally and vertically
        self.response_text.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # Disable rich text - use plain text for proper line breaks
        self.response_text.setAcceptRichText(False)

        # Set larger monospace font for better visibility
        code_font = QFont("Consolas", 15)  # Increased from 13 to 15
        if not code_font.exactMatch():
            code_font = QFont("Courier New", 15)
        code_font.setFixedPitch(True)  # Ensure monospace
        self.response_text.setFont(code_font)

        self.response_text.setStyleSheet("""
            QTextEdit {
                background-color: #f0f8ff;
                border: 2px solid #007bff;
                border-radius: 8px;
                padding: 12px;
                font-size: 15px;
                color: #333;
                line-height: 1.5;
                font-family: 'Consolas', 'Courier New', monospace;
            }
        """)
        self.response_text.setPlaceholderText("Full-stack developer answers will appear here with examples...")
        # Add with stretch factor to make it expand
        layout.addWidget(self.response_text, 1)  # Stretch factor of 1 to expand

        # Status label - Fixed height
        status_frame = QFrame()
        status_frame.setFixedHeight(30)  # Fixed height
        status_frame.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)
        status_layout = QVBoxLayout(status_frame)
        status_layout.setContentsMargins(0, 0, 0, 0)

        self.status_label = QLabel("🎤 Starting interview mode... Ready for technical questions!")
        self.status_label.setStyleSheet("color: #666; font-size: 10px; padding: 5px; background-color: #f0f0f0; border-radius: 4px;")
        self.status_label.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)
        status_layout.addWidget(self.status_label)
        layout.addWidget(status_frame)

        # Instructions label - Fixed height
        instructions_frame = QFrame()
        instructions_frame.setFixedHeight(25)  # Fixed height
        instructions_frame.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)
        instructions_layout = QVBoxLayout(instructions_frame)
        instructions_layout.setContentsMargins(0, 0, 0, 0)

        instructions_label = QLabel("💡 Tips: Ask frontend, backend, database, or system design questions | Ctrl+Space (hide/show) | Ctrl+Arrows (move)")
        instructions_label.setStyleSheet("color: #888; font-size: 9px; padding: 4px;")
        instructions_label.setWordWrap(True)
        instructions_label.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)
        instructions_layout.addWidget(instructions_label)
        layout.addWidget(instructions_frame)

        # Resize grip
        self.resize_grip = QSizeGrip(self)
        layout.addWidget(self.resize_grip, 0, Qt.AlignBottom | Qt.AlignRight)

        # Position window at bottom-right
        self.position_window_bottom_right()

        # Auto-start transcription immediately after UI is ready
        QTimer.singleShot(500, self.auto_start_transcription)

    def setup_keyboard_shortcuts(self):
        """Setup fast and smooth keyboard shortcuts"""
        # Enable focus and key events
        self.setFocusPolicy(Qt.StrongFocus)
        self.setFocus()

        # Very fast timer for responsive controls
        self.key_timer = QTimer()
        self.key_timer.timeout.connect(self.check_keys)
        self.key_timer.start(25)  # Check every 25ms for very fast movement

        # Key state tracking
        self.key_states = {
            'space': False,
            'up': False,
            'down': False,
            'left': False,
            'right': False,
            'caps_up': False,
            'caps_down': False
        }

        # Make window always on top for shortcuts to work
        self.setWindowFlags(self.windowFlags() | Qt.WindowStaysOnTopHint)

        # Ensure window can receive key events
        self.setAttribute(Qt.WA_InputMethodEnabled, True)
        self.setAttribute(Qt.WA_KeyCompression, False)  # Disable key compression for better responsiveness

        # Try to grab keyboard, but don't fail if it doesn't work
        try:
            self.grabKeyboard()  # Capture all keyboard input
            print("✅ Keyboard grabbed successfully")
        except Exception as e:
            print(f"⚠️ Could not grab keyboard: {e}")

        # Ensure response text can also receive focus for scrolling
        self.response_text.setFocusPolicy(Qt.StrongFocus)

    def check_keys(self):
        """Check for key combinations with continuous movement support"""
        try:
            # Check if Ctrl is pressed
            ctrl_pressed = ctypes.windll.user32.GetAsyncKeyState(0x11) & 0x8000

            # Check if CapsLock is on
            caps_lock_on = ctypes.windll.user32.GetKeyState(0x14) & 0x0001

            # CapsLock + Arrow Keys: Continuous scroll response text
            if caps_lock_on:
                up_now = ctypes.windll.user32.GetAsyncKeyState(0x26) & 0x8000
                down_now = ctypes.windll.user32.GetAsyncKeyState(0x28) & 0x8000

                # Continuous scrolling up while key is held
                if up_now:
                    try:
                        scrollbar = self.response_text.verticalScrollBar()
                        if scrollbar and scrollbar.isVisible():
                            current_value = scrollbar.value()
                            min_value = scrollbar.minimum()
                            # Only scroll if not already at top
                            if current_value > min_value:
                                new_value = max(min_value, current_value - 30)  # Smaller step for smoother scrolling
                                scrollbar.setValue(new_value)
                                # Debug output and status update
                                if not hasattr(self, '_debug_scroll_up'):
                                    print(f"🔼 CapsLock+Up: Scrolling from {current_value} to {new_value}")
                                    self.status_label.setText("🔼 CapsLock+Up: Scrolling response text...")
                                    self._debug_scroll_up = True
                    except Exception as e:
                        print(f"❌ Error scrolling up: {e}")
                else:
                    if hasattr(self, '_debug_scroll_up'):
                        delattr(self, '_debug_scroll_up')

                # Continuous scrolling down while key is held
                if down_now:
                    try:
                        scrollbar = self.response_text.verticalScrollBar()
                        if scrollbar and scrollbar.isVisible():
                            current_value = scrollbar.value()
                            max_value = scrollbar.maximum()
                            # Only scroll if not already at bottom
                            if current_value < max_value:
                                new_value = min(max_value, current_value + 30)  # Smaller step for smoother scrolling
                                scrollbar.setValue(new_value)
                                # Debug output and status update
                                if not hasattr(self, '_debug_scroll_down'):
                                    print(f"🔽 CapsLock+Down: Scrolling from {current_value} to {new_value}")
                                    self.status_label.setText("🔽 CapsLock+Down: Scrolling response text...")
                                    self._debug_scroll_down = True
                    except Exception as e:
                        print(f"❌ Error scrolling down: {e}")
                else:
                    if hasattr(self, '_debug_scroll_down'):
                        delattr(self, '_debug_scroll_down')

            elif ctrl_pressed:
                # Ctrl + Space - toggle window visibility (single press only)
                space_now = ctypes.windll.user32.GetAsyncKeyState(0x20) & 0x8000
                if space_now and not self.key_states['space']:
                    self.key_states['space'] = True
                    self.toggle_window_visibility()
                elif not space_now:
                    self.key_states['space'] = False

                # Arrow keys - continuous movement when held
                up_now = ctypes.windll.user32.GetAsyncKeyState(0x26) & 0x8000
                if up_now:
                    if not self.key_states['up']:
                        self.key_states['up'] = True
                    self.move_window("up")  # Move continuously while held
                else:
                    self.key_states['up'] = False

                down_now = ctypes.windll.user32.GetAsyncKeyState(0x28) & 0x8000
                if down_now:
                    if not self.key_states['down']:
                        self.key_states['down'] = True
                    self.move_window("down")  # Move continuously while held
                else:
                    self.key_states['down'] = False

                left_now = ctypes.windll.user32.GetAsyncKeyState(0x25) & 0x8000
                if left_now:
                    if not self.key_states['left']:
                        self.key_states['left'] = True
                    self.move_window("left")  # Move continuously while held
                else:
                    self.key_states['left'] = False

                right_now = ctypes.windll.user32.GetAsyncKeyState(0x27) & 0x8000
                if right_now:
                    if not self.key_states['right']:
                        self.key_states['right'] = True
                    self.move_window("right")  # Move continuously while held
                else:
                    self.key_states['right'] = False
            else:
                # Reset all key states when Ctrl is not pressed
                for key in self.key_states:
                    self.key_states[key] = False
        except:
            pass

    def position_window_bottom_right(self):
        """Position window at bottom-right corner"""
        screen = QApplication.desktop().screenGeometry()
        window_size = self.geometry()
        x = screen.width() - window_size.width() - 20
        y = screen.height() - window_size.height() - 60
        self.move(x, y)

    def setup_always_on_top_enforcement(self):
        """Setup periodic enforcement of always-on-top behavior"""
        # Timer to periodically ensure window stays on top
        self.always_on_top_timer = QTimer()
        self.always_on_top_timer.timeout.connect(self.enforce_always_on_top)
        self.always_on_top_timer.start(2000)  # Check every 2 seconds

    def enforce_always_on_top(self):
        """Ensure window stays on top"""
        try:
            if self.isVisible():
                # Re-raise window to top
                self.raise_()
                self.activateWindow()

                # Use Windows API to force window to top if available
                if hasattr(self, 'hwnd'):
                    HWND_TOPMOST = -1
                    SWP_NOSIZE = 0x0001
                    SWP_NOMOVE = 0x0002
                    SWP_SHOWWINDOW = 0x0040

                    ctypes.windll.user32.SetWindowPos(
                        self.hwnd, HWND_TOPMOST, 0, 0, 0, 0,
                        SWP_NOSIZE | SWP_NOMOVE | SWP_SHOWWINDOW
                    )
        except Exception as e:
            # Silently handle any errors to avoid disrupting the application
            pass

    def apply_stealth_features(self):
        """Apply stealth features to hide from screen capture"""
        # Show the window first
        self.show()
        self.raise_()
        self.activateWindow()

        # Apply stealth after window is rendered
        QTimer.singleShot(500, self.hide_from_capture)
        QTimer.singleShot(1000, self.ensure_local_visibility)

    def hide_from_capture(self):
        """Enhanced hide window from screen capture (Windows 10/11 compatible)"""
        try:
            hwnd = int(self.winId())

            # PRIMARY METHOD: Set window to be excluded from capture (Windows 10+)
            WDA_EXCLUDEFROMCAPTURE = 0x00000011
            result1 = ctypes.windll.user32.SetWindowDisplayAffinity(hwnd, WDA_EXCLUDEFROMCAPTURE)

            # SECONDARY METHOD: Only apply if LOCAL_VISIBILITY_MODE is disabled
            if result1 and not LOCAL_VISIBILITY_MODE:
                # Make window non-enumerable (harder to detect) - only if local visibility not needed
                GWL_EXSTYLE = -20
                WS_EX_NOACTIVATE = 0x08000000
                WS_EX_TOOLWINDOW = 0x00000080

                current_style = ctypes.windll.user32.GetWindowLongW(hwnd, GWL_EXSTYLE)
                new_style = current_style | WS_EX_NOACTIVATE | WS_EX_TOOLWINDOW

                result2 = ctypes.windll.user32.SetWindowLongW(hwnd, GWL_EXSTYLE, new_style)

                # Set window to bottom of Z-order for additional stealth
                HWND_BOTTOM = 1
                SWP_NOSIZE = 0x0001
                SWP_NOMOVE = 0x0002
                SWP_NOACTIVATE = 0x0010

                ctypes.windll.user32.SetWindowPos(
                    hwnd, HWND_BOTTOM, 0, 0, 0, 0,
                    SWP_NOSIZE | SWP_NOMOVE | SWP_NOACTIVATE
                )

            # Store hwnd for later use
            self.hwnd = hwnd

            # Hide from taskbar if configured
            if ENABLE_TASKBAR_HIDING:
                self.hide_from_taskbar()

        except Exception as e:
            if DEBUG_STEALTH_FEATURES:
                print(f"❌ Error hiding from capture: {e}")

    def hide_from_taskbar(self):
        """Hide window from taskbar"""
        try:
            if ENABLE_TASKBAR_HIDING:
                hwnd = int(self.winId())

                # Hide from taskbar and Alt+Tab
                GWL_EXSTYLE = -20
                WS_EX_TOOLWINDOW = 0x00000080

                current_style = ctypes.windll.user32.GetWindowLongW(hwnd, GWL_EXSTYLE)
                new_style = current_style | WS_EX_TOOLWINDOW
                ctypes.windll.user32.SetWindowLongW(hwnd, GWL_EXSTYLE, new_style)

                if DEBUG_STEALTH_FEATURES:
                    print("✅ Window hidden from taskbar")
        except Exception as e:
            if DEBUG_STEALTH_FEATURES:
                print(f"❌ Error hiding from taskbar: {e}")

    def ensure_local_visibility(self):
        """Ensure window remains visible locally"""
        try:
            self.show()
            self.raise_()
            self.setWindowOpacity(WINDOW_TRANSPARENCY)

            if hasattr(self, 'hwnd'):
                HWND_TOPMOST = -1
                SWP_NOSIZE = 0x0001
                SWP_NOMOVE = 0x0002
                SWP_SHOWWINDOW = 0x0040

                ctypes.windll.user32.SetWindowPos(
                    self.hwnd, HWND_TOPMOST, 0, 0, 0, 0,
                    SWP_NOSIZE | SWP_NOMOVE | SWP_SHOWWINDOW
                )
        except Exception as e:
            pass

    def auto_start_transcription(self):
        """Automatically start transcription when application loads"""
        try:
            self.transcriber = RealTimeTranscriber()

            # Connect signals
            self.transcriber.text_updated.connect(self.update_question_text)
            self.transcriber.final_text.connect(self.update_final_text)
            self.transcriber.ai_response.connect(self.update_response_text)

            # Start transcription
            self.transcriber.start_transcription()

            # Update UI - Always listening
            self.status_label.setText("🎤 Always Listening...")
            self.question_text.setText("Speak your technical question and it will appear here...")  # setText for QLabel
            self.response_text.clear()

        except Exception as e:
            self.status_label.setText(f"Error: {str(e)}")

    def update_question_text(self, text):
        """Update real-time question text"""
        self.question_text.setText(text)  # Changed from setPlainText to setText for QLabel

        # Update status based on current mode
        if hasattr(self, 'transcriber') and self.transcriber:
            mode = self.transcriber.question_mode.get_mode()
            if mode == "PROCESSING":
                self.status_label.setText("🎤 Processing interview question...")
            elif mode == "TRIGGERED":
                self.status_label.setText("🎯 Ready for question - speak now...")
            else:
                self.status_label.setText("🗣️ Listening to your question...")
        else:
            self.status_label.setText("🗣️ Listening to your question...")

    def update_final_text(self, text):
        """Update final transcribed text"""
        self.question_text.setText(text)  # Changed from setPlainText to setText for QLabel
        self.status_label.setText("🤖 Preparing developer answer...")

    def update_mode_status(self, mode):
        """Update UI based on current processing mode"""
        mode_messages = {
            "IDLE": "🎧 Listening for interview questions... (Ctrl+Space to activate)",
            "TRIGGERED": "🎯 Question mode active - speak your interview question now!",
            "PROCESSING": "🎤 Processing your interview question...",
            "RESPONDING": "🤖 Generating technical answer..."
        }

        if mode in mode_messages:
            self.status_label.setText(mode_messages[mode])

    def update_response_text(self, response_with_model):
        """Update AI response text with streaming support"""
        # Extract response and model name
        if "|MODEL:" in response_with_model:
            response, model_info = response_with_model.split("|MODEL:", 1)
            model_name = model_info.strip()
        else:
            response = response_with_model
            model_name = "Unknown"

        # Update response label with model name (only once)
        if not hasattr(self, 'current_model') or self.current_model != model_name:
            self.current_model = model_name
            self.response_label.setText(f"💡 Developer Answer (via {model_name}):")

            # Clear response text for new streaming and reset scroll position to top
            if response.strip():  # Only clear if there's actual content
                self.response_text.clear()
                # Reset scroll position to top for new response
                self.response_text.verticalScrollBar().setValue(0)
                # Track that this is a new response
                self.is_new_response = True

        # Update response text (streaming) with proper formatting
        if response.strip():  # Only update if there's content
            # Clean up response and ensure proper formatting
            cleaned_response = self.clean_response_format(response)

            # Use setPlainText to preserve line breaks and formatting
            self.response_text.setPlainText(cleaned_response)

            # Keep view at top for new responses - NO AUTO-SCROLL to bottom
            # This allows reading from the beginning while response is being generated
            if hasattr(self, 'is_new_response') and self.is_new_response:
                # Keep scroll position at top to show beginning of response
                self.response_text.verticalScrollBar().setValue(0)
                # Move cursor to beginning instead of end
                cursor = self.response_text.textCursor()
                cursor.movePosition(cursor.Start)
                self.response_text.setTextCursor(cursor)
                # Mark that we've handled the new response positioning
                self.is_new_response = False

            # Update status to show streaming
            self.status_label.setText(f"📝 Streaming answer from {model_name}...")

        # Check if this is the final chunk (no more updates expected)
        QTimer.singleShot(200, self.check_streaming_complete)

    def check_streaming_complete(self):
        """Check if streaming is complete and update status"""
        if hasattr(self, 'current_model'):
            self.status_label.setText(f"✅ Answer ready! Ask another technical question...")

    def toggle_window_visibility(self):
        """Fast toggle window visibility"""
        if self.isVisible():
            self.hide()
        else:
            self.show()
            self.raise_()
            self.activateWindow()

    def hide_window(self):
        """Hide the window"""
        self.hide()

    def show_window(self):
        """Show the window"""
        self.show()
        self.raise_()
        self.activateWindow()
        
    def move_window(self, direction):
        """Move window continuously when key is held"""
        current_pos = self.pos()
        move_distance = 35  # Very fast movement distance

        # Calculate new position
        if direction == "up":
            new_x, new_y = current_pos.x(), current_pos.y() - move_distance
        elif direction == "down":
            new_x, new_y = current_pos.x(), current_pos.y() + move_distance
        elif direction == "left":
            new_x, new_y = current_pos.x() - move_distance, current_pos.y()
        elif direction == "right":
            new_x, new_y = current_pos.x() + move_distance, current_pos.y()
        else:
            return

        # Quick bounds check
        screen = QApplication.desktop().screenGeometry()
        window_size = self.size()

        # Clamp to screen bounds
        x = max(0, min(new_x, screen.width() - window_size.width()))
        y = max(0, min(new_y, screen.height() - window_size.height()))

        # Move window
        self.move(x, y)

    def title_mouse_press(self, event):
        """Handle title bar click to focus window for keyboard shortcuts"""
        if event.button() == Qt.LeftButton:
            self.setFocus()  # Give focus to window for keyboard shortcuts
            self.drag_start_position = event.globalPos()

    def keyPressEvent(self, event):
        """Handle key press events for additional keyboard shortcuts"""
        # Check for force exit shortcuts
        if event.modifiers() & Qt.ControlModifier:
            if event.key() == Qt.Key_Q:  # Ctrl+Q for force exit
                print("🔄 Ctrl+Q pressed - Force exiting...")
                self.force_exit()
                return
            elif event.key() == Qt.Key_W:  # Ctrl+W for normal close
                print("🔄 Ctrl+W pressed - Closing window...")
                self.close()
                return
            elif event.key() == Qt.Key_Space:  # Ctrl+Space for manual question activation
                if hasattr(self, 'transcriber') and self.transcriber:
                    self.transcriber.activate_question_mode_manually()
                    self.update_mode_status("TRIGGERED")
                    print("🎯 Ctrl+Space pressed - Question mode activated!")
                return

        # Alt+F4 for force exit
        if event.modifiers() & Qt.AltModifier and event.key() == Qt.Key_F4:
            print("🔄 Alt+F4 pressed - Force exiting...")
            self.force_exit()
            return

        # Escape key for force exit
        if event.key() == Qt.Key_Escape:
            print("🔄 Escape pressed - Force exiting...")
            self.force_exit()
            return

        # Check if CapsLock is on using Qt's native method
        caps_lock_on = event.modifiers() & Qt.ShiftModifier or bool(ctypes.windll.user32.GetKeyState(0x14) & 0x0001)

        # Handle CapsLock + Arrow keys for scrolling (fallback method)
        if caps_lock_on:
            if event.key() == Qt.Key_Up:
                try:
                    scrollbar = self.response_text.verticalScrollBar()
                    current_value = scrollbar.value()
                    scrollbar.setValue(current_value - 30)  # Scroll up
                    print(f"🔼 Qt KeyPress: CapsLock+Up scrolling to {scrollbar.value()}")
                    event.accept()
                    return
                except Exception as e:
                    print(f"❌ Error in keyPressEvent Up: {e}")

            elif event.key() == Qt.Key_Down:
                try:
                    scrollbar = self.response_text.verticalScrollBar()
                    current_value = scrollbar.value()
                    scrollbar.setValue(current_value + 30)  # Scroll down
                    print(f"🔽 Qt KeyPress: CapsLock+Down scrolling to {scrollbar.value()}")
                    event.accept()
                    return
                except Exception as e:
                    print(f"❌ Error in keyPressEvent Down: {e}")

        # Pass other key events to parent
        super().keyPressEvent(event)

    def mousePressEvent(self, event):
        """Handle mouse press for window dragging"""
        if event.button() == Qt.LeftButton:
            self.setFocus()  # Give focus to window
            self.drag_start_position = event.globalPos()

    def mouseMoveEvent(self, event):
        """Handle mouse move for window dragging"""
        if event.buttons() == Qt.LeftButton and hasattr(self, 'drag_start_position'):
            self.move(self.pos() + event.globalPos() - self.drag_start_position)
            self.drag_start_position = event.globalPos()

    def clean_response_format(self, response):
        """Clean and format AI response for proper display"""
        # If response contains code mixed with definition, try to separate it
        lines = response.split('\n')
        cleaned_lines = []

        for line in lines:
            # Check if line contains both definition and code mixed together
            if ('function' in line.lower() or 'class' in line.lower() or
                'def ' in line.lower() or 'const ' in line.lower()) and len(line) > 80:

                # Try to split definition and code
                if 'Example:' in line:
                    parts = line.split('Example:', 1)
                    cleaned_lines.append(parts[0].strip())
                    cleaned_lines.append('')
                    cleaned_lines.append('Example:')

                    # Process code part
                    code_part = parts[1].strip()
                    # Simple code formatting - split on common patterns
                    code_part = code_part.replace('{ ', '{\n    ')
                    code_part = code_part.replace('; ', ';\n    ')
                    code_part = code_part.replace('} ', '}\n')
                    cleaned_lines.append(code_part)
                else:
                    cleaned_lines.append(line)
            else:
                cleaned_lines.append(line)

        return '\n'.join(cleaned_lines)

    def closeEvent(self, event):
        """Handle window close event with proper cleanup"""
        try:
            print("🔄 Application closing - starting cleanup...")

            # Release keyboard grab
            self.releaseKeyboard()

            # Stop transcriber and all its threads
            if self.transcriber:
                print("🔄 Stopping transcriber...")
                self.transcriber.stop_transcription()
                # Wait for transcriber thread to finish
                if self.transcriber.isRunning():
                    self.transcriber.quit()
                    self.transcriber.wait(3000)  # Wait up to 3 seconds
                    if self.transcriber.isRunning():
                        self.transcriber.terminate()  # Force terminate if needed

            # Stop time tracking timer
            if hasattr(self, 'time_timer') and self.time_timer:
                self.time_timer.stop()

            # Clean up temporary files
            cleanup_temp_directory()

            # Force close any remaining threads
            import threading
            for thread in threading.enumerate():
                if thread != threading.current_thread() and thread.daemon:
                    try:
                        thread.join(timeout=1)  # Wait 1 second for daemon threads
                    except:
                        pass

            print("✅ Cleanup completed")
            event.accept()

            # Force application exit
            QApplication.quit()

        except Exception as e:
            print(f"❌ Error during close: {e}")
            event.accept()
            # Force exit even if cleanup fails
            QApplication.quit()

    def force_exit(self):
        """Force exit the application immediately"""
        try:
            print("🔄 Force exiting application...")

            # Stop all threads immediately
            if self.transcriber:
                self.transcriber.terminate()

            # Force cleanup
            cleanup_temp_directory()

            # Force quit
            QApplication.quit()

            # Ultimate fallback - force process termination
            import os
            os._exit(0)

        except:
            # Ultimate fallback
            import os
            os._exit(0)

def main():
    """Main function optimized for exe"""
    try:
        print("🚀 Starting InterV AI...")

        # Set working directory for exe
        if getattr(sys, 'frozen', False):
            exe_dir = os.path.dirname(sys.executable)
            os.chdir(exe_dir)
            print(f"📁 Working directory: {exe_dir}")

        # Create QApplication
        print("🔄 Creating QApplication...")
        app = QApplication(sys.argv)
        app.setQuitOnLastWindowClosed(True)
        app.setApplicationName("InterV AI - Full Stack Developer")
        app.setApplicationVersion("2.0")

        # Handle application quit event
        app.aboutToQuit.connect(lambda: print("🔄 Application about to quit..."))

        # Create and show main window
        print("🔄 Creating main window...")
        window = LiveTranscriptionApp()

        if window:
            print("✅ Main window created successfully")
            window.show()
            print("✅ Application started successfully")

            # Setup signal handlers for proper cleanup
            import signal
            def signal_handler(signum, frame):
                print(f"🔄 Received signal {signum}, cleaning up...")
                if window:
                    window.close()
                app.quit()
                sys.exit(0)

            # Handle Ctrl+C and other termination signals
            try:
                signal.signal(signal.SIGINT, signal_handler)
                signal.signal(signal.SIGTERM, signal_handler)
            except:
                pass  # Some signals may not be available on all platforms

            # Run the application
            exit_code = app.exec_()

            # Final cleanup
            print("🔄 Application exiting, final cleanup...")
            cleanup_temp_directory()
            sys.exit(exit_code)
        else:
            print("❌ Failed to create main window")
            sys.exit(1)

    except Exception as e:
        print(f"❌ Application error: {e}")
        import traceback
        traceback.print_exc()

        # Show error dialog for debugging
        if not getattr(sys, 'frozen', False):
            try:
                from PyQt5.QtWidgets import QMessageBox
                app = QApplication.instance()
                if not app:
                    app = QApplication(sys.argv)

                msg = QMessageBox()
                msg.setIcon(QMessageBox.Critical)
                msg.setWindowTitle("InterV AI Error")
                msg.setText(f"Application failed to start:\n\n{str(e)}")
                msg.exec_()
            except:
                pass

        sys.exit(1)

if __name__ == "__main__":
    main()

# Build command for creating LIGHTWEIGHT exe:
# pip install pyinstaller PyQt5 pyaudio faster-whisper numpy requests firebase-admin
# python -m PyInstaller --onefile --noconsole --hidden-import=faster_whisper --hidden-import=PyQt5.QtCore --hidden-import=PyQt5.QtGui --hidden-import=PyQt5.QtWidgets --hidden-import=pyaudio --hidden-import=numpy --hidden-import=firebase_admin --exclude-module=torch --exclude-module=tensorflow --exclude-module=matplotlib --exclude-module=scipy --exclude-module=pandas --optimize=2 --strip --name=InterV_AI_Optimized live.py 